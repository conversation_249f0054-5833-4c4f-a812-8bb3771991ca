# CI/CD Setup for FajiraPro

This document explains the Continuous Integration and Continuous Deployment (CI/CD) setup for the FajiraPro project.

## Overview

FajiraPro uses a combination of GitHub Actions for CI (testing and building) and direct GitHub integrations for CD (deployment):

1. **GitHub Actions**: Run tests and build the code
2. **Cloudflare Pages GitHub Integration**: Deploy the frontend
3. **Railway GitHub Integration**: Deploy the backend

This approach provides a streamlined deployment process while ensuring code quality through automated testing.

## GitHub Actions Workflows

### Frontend CI Workflow

The frontend CI workflow (`frontend-ci-cd.yml`) performs the following steps:

1. Checkout the code
2. Set up Node.js
3. Install dependencies
4. Run linting
5. Run tests
6. Build the application
7. Verify the build

The workflow is triggered on:
- Push to `main` or `develop` branches (when frontend files change)
- Pull requests to `main` or `develop` branches (when frontend files change)

### Backend CI Workflow

The backend CI workflow (`backend-ci-cd.yml`) performs the following steps:

1. Checkout the code
2. Set up Node.js
3. Install Node dependencies
4. Run Node linting and tests
5. Set up Python
6. Install Python dependencies
7. Run Python tests
8. Verify the build

The workflow is triggered on:
- Push to `main` or `develop` branches (when backend files change)
- Pull requests to `main` or `develop` branches (when backend files change)

## Deployment Process

### Frontend Deployment (Cloudflare Pages)

The frontend is automatically deployed by Cloudflare Pages:

1. When code is pushed to the GitHub repository, Cloudflare Pages detects the changes
2. Cloudflare Pages builds the frontend application using the configured build settings:
   - Build command: `cd AjiraPro/frontend && npm install --legacy-peer-deps && npm run build`
   - Build output directory: `AjiraPro/frontend/build`
3. The built application is deployed to Cloudflare's global CDN
4. The deployment is available at https://fajirapro.com

### Backend Deployment (Railway)

The backend is automatically deployed by Railway:

1. When code is pushed to the GitHub repository, Railway detects the changes
2. Railway builds and deploys the backend services:
   - FastAPI Service (AjiraProMax project)
   - Redis Service
   - Celery Worker Service (adventurous-bravery)
3. Each service is configured with the appropriate environment variables
4. The deployment is available at the Railway-provided URL

## Branch Strategy

The project follows a GitFlow-inspired branching strategy:

- `main`: Production-ready code
- `develop`: Integration branch for feature development
- `feature/*`: New features and non-emergency bugfixes
- `hotfix/*`: Urgent production fixes
- `release/*`: Release preparation

## Deployment Environments

Currently, the project is primarily configured for a single deployment environment:

1. **Production**: Deployed from the `main` branch

To set up a staging environment for testing:

1. Configure Cloudflare Pages to create preview deployments for the `develop` branch
2. Configure Railway to deploy from the `develop` branch to a staging environment

This would enable a proper testing workflow before deploying to production.

## Monitoring Deployments

### Frontend Deployments

Monitor frontend deployments in the Cloudflare Pages dashboard:
- View build logs
- Check deployment status
- Access deployment previews

### Backend Deployments

Monitor backend deployments in the Railway dashboard:
- View build and deployment logs
- Check service status
- Monitor resource usage

## Troubleshooting

### Common Issues

1. **Failed Builds**:
   - Check the GitHub Actions logs for errors
   - Verify that all dependencies are correctly specified
   - Ensure tests are passing

2. **Failed Deployments**:
   - Check the Cloudflare Pages or Railway logs
   - Verify environment variables are correctly set
   - Check for syntax errors in configuration files

### Rollback Procedure

If a deployment introduces issues:

1. **Frontend Rollback**:
   - Go to the Cloudflare Pages dashboard
   - Find the previous successful deployment
   - Click "Rollback to this deployment"

2. **Backend Rollback**:
   - Go to the Railway dashboard
   - Select the service with issues
   - Go to the "Deployments" tab
   - Find the last working deployment
   - Click "Rollback to this deployment"

## Testing the CI/CD Pipeline

To test the CI/CD pipeline:

1. Make changes to the codebase
2. Commit and push the changes to a feature branch
3. Create a pull request to the develop branch
4. Verify that the CI/CD workflow runs and passes
5. Merge the pull request
6. Verify that the changes are deployed to the develop branch
7. Create a pull request from develop to main
8. Verify that the CI/CD workflow runs and passes
9. Merge the pull request
10. Verify that the changes are deployed to the production environment

### Local Testing Before Deployment

Before pushing changes, you should test them locally:

1. **Frontend Testing**:
   ```bash
   cd AjiraPro/frontend
   npm start
   # Access at http://localhost:3000
   ```

2. **Backend Testing**:
   ```bash
   cd AjiraPro/backend
   uvicorn app.main:app --reload
   # Access at http://localhost:8000
   ```

This ensures that your changes work correctly before triggering the CI/CD pipeline.