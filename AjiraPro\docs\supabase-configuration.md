# Supabase Configuration Documentation

This document provides a detailed overview of the Supabase configuration for the AjiraPro project, including database schema, storage buckets, security policies, and authentication setup.

## Project Configuration

- **Region**: Frankfurt (eu-central-1)
  - Selected for proximity to Kenya and other parts of Africa
  - Provides low latency for African users
  - Offers GDPR compliance and excellent connectivity to East Africa
  - Alternative options (in order of preference):
    - London (eu-west-2): Also offers good connectivity to Africa
    - Singapore (ap-southeast-1): Can be a decent option if Frankfurt and London have higher latency
  - Not recommended:
    - US regions (us-east-1, us-west-1, etc.) would have higher latency for users in Kenya
    - Australia regions would have very high latency

- **Connection Pooling**: Shared Pooler (Transaction pooler)
  - Ideal for stateless applications like serverless functions
  - Optimized for brief, isolated interactions with Postgres

## Database Schema

The database schema was created using the `schema.sql` script, which set up the following tables:

### Tables

#### 1. profiles
- Stores user profile information
- Created automatically when a new user signs up
- Contains user details like name, email, and subscription status

#### 2. resumes
- Stores user resume information
- Links to the user via user_id
- Contains resume content in JSON format

#### 3. drafts_resume
- Stores draft versions of resumes
- Allows users to save work in progress

#### 4. payments
- Tracks payment information
- Records subscription purchases and status

#### 5. templates_resume
- Stores resume templates
- Contains template content and metadata

#### 6. job_descriptions
- Stores job descriptions for resume tailoring
- Links to the user via user_id

#### 7. admin_users
- Stores IDs of users with administrative privileges
- Used for access control to admin-only features

## Storage Buckets

Three storage buckets have been created with specific configurations:

### 1. resumes (Private)
- **Purpose**: Stores generated resume files
- **Access**: Private (only accessible to the file owner)
- **File Size Limit**: 5MB (5242880 bytes)
- **Allowed MIME Types**: application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document
- **Policies**:
  - Users can only view their own resumes: `bucket_id = 'resumes' AND auth.uid() = owner`
  - Users can only upload their own resumes: `bucket_id = 'resumes' AND auth.uid() = owner`
  - Users can only update their own resumes: `bucket_id = 'resumes' AND auth.uid() = owner`
  - Users can only delete their own resumes: `bucket_id = 'resumes' AND auth.uid() = owner`

### 2. templates (Public)
- **Purpose**: Stores template files for resumes
- **Access**: Public read, admin-only write
- **File Size Limit**: 5MB (5242880 bytes)
- **Allowed MIME Types**: application/vnd.openxmlformats-officedocument.wordprocessingml.document
- **Policies**:
  - Anyone can view templates: `bucket_id = 'templates' AND true`
  - Only admins can upload templates: `bucket_id = 'templates' AND auth.uid() IN (SELECT id FROM admin_users)`
  - Only admins can update templates: `bucket_id = 'templates' AND auth.uid() IN (SELECT id FROM admin_users)`
  - Only admins can delete templates: `bucket_id = 'templates' AND auth.uid() IN (SELECT id FROM admin_users)`

### 3. avatars (Public)
- **Purpose**: Stores user profile pictures
- **Access**: Public read, user-only write
- **File Size Limit**: 2MB (2097152 bytes)
- **Allowed MIME Types**: image/jpeg, image/png, image/gif, image/webp
- **Policies**:
  - Anyone can view avatars: `bucket_id = 'avatars' AND true`
  - Users can only upload their own avatars: `bucket_id = 'avatars' AND auth.uid() = owner`
  - Users can only update their own avatars: `bucket_id = 'avatars' AND auth.uid() = owner`
  - Users can only delete their own avatars: `bucket_id = 'avatars' AND auth.uid() = owner`

## Policy Naming Conventions

When creating policies in Supabase, you'll notice that automatically generated prefixes (like "24bk_0") are added to policy names. These prefixes are:

- **Purpose**: Ensure policy names are unique within the database
- **Format**: Usually a combination of characters and numbers (e.g., "24bk_0")
- **Behavior**:
  - These prefixes are normal and don't affect how policies work
  - They are part of Supabase's internal management system
  - Do not attempt to remove or modify these prefixes as it may cause issues
  - Focus on the descriptive part of the policy name that you provide

## Row Level Security (RLS) Policies

Row Level Security policies have been implemented to ensure data security:

### profiles Table
- Users can only view their own profile
- Users can only update their own profile

### resumes Table
- Users can only view their own resumes
- Users can only insert their own resumes
- Users can only update their own resumes
- Users can only delete their own resumes

### drafts_resume Table
- Users can only view their own drafts
- Users can only insert their own drafts
- Users can only update their own drafts
- Users can only delete their own drafts

### payments Table
- Users can only view their own payments
- Users can only insert their own payments

### templates_resume Table
- All users can view templates
- Only admins can insert, update, or delete templates

### job_descriptions Table
- Users can only view job descriptions for their own resumes
- Users can only insert job descriptions for their own resumes
- Users can only update job descriptions for their own resumes
- Users can only delete job descriptions for their own resumes

## Database Functions

### handle_new_user()
- **Trigger**: After INSERT on auth.users
- **Purpose**: Automatically creates a profile when a new user signs up
- **Logic**: Inserts a new record into the profiles table with the user's ID and email

### update_updated_at_column()
- **Trigger**: Before UPDATE on tables with updated_at column
- **Purpose**: Automatically updates the updated_at timestamp
- **Logic**: Sets the updated_at column to the current timestamp

## Authentication Configuration

### Email Authentication
- Enabled for user registration and login
- Email confirmation required

### OAuth Providers
- Google authentication enabled
- Redirect URL: https://ajirapro.com/auth/callback

## Admin User Management

The admin_users table stores the UUIDs of users with administrative privileges:

- **Finding User IDs**:
  - From Supabase Dashboard: Authentication > Users > ID column
  - From Database: auth schema > users table > id column
  - From Application Code: `const { data: { user } } = await supabase.auth.getUser(); const userId = user.id;`

- **Adding Admin Users**:
  - First create a user through the authentication system
  - Get the user's UUID
  - Insert the UUID into the admin_users table:
    ```sql
    INSERT INTO admin_users (id) VALUES ('user-uuid-here');
    ```

## API Keys

Two types of API keys are used:

### anon/public key
- Used for client-side access
- Subject to Row Level Security policies
- Used in frontend applications

### service_role key
- Has full admin access to the database
- Bypasses Row Level Security policies
- Used only in secure server-side code
- Never exposed to clients

## Frontend Integration

A Supabase client utility has been created in `frontend/src/utils/supabase.ts` with helper functions for:
- Authentication
- Profile management
- File storage operations

## Backend Integration

The backend integrates with Supabase through:
- Supabase client services for database operations
- Authentication middleware for JWT validation
- API endpoints for user management

## Environment Variables

The following environment variables are required:

### Frontend (.env.local)
```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Backend (.env)
```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
DATABASE_URL=your_direct_database_connection_string
```

Note: The DATABASE_URL is different from the SUPABASE_URL and should be obtained from the Supabase dashboard under Project Settings > Database.

## Security Best Practices

1. **Use RLS Policies**: Always define Row Level Security policies for all tables
2. **Validate JWTs**: Verify Supabase JWT tokens on the backend
3. **Use anon key for client**: Never expose the service_role key to clients
4. **Secure file storage**: Use signed URLs for private file access
5. **Validate input**: Always validate user input before storing in the database

## Maintenance and Monitoring

- Regular backups are enabled
- Point-in-time recovery is available
- Database logs can be accessed through the Supabase dashboard

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**: If you're getting permission denied errors, check your RLS policies
2. **JWT Validation Failures**: Ensure your JWT token is valid and not expired
3. **Storage Access Issues**: Verify the bucket policies are correctly configured
4. **Database Connection Problems**: Check your DATABASE_URL and connection pooling settings

### Debugging Tips

1. Use the Supabase dashboard to view logs and debug issues
2. Test RLS policies using the SQL editor
3. Verify JWT tokens using a JWT debugger
4. Check storage bucket permissions in the Supabase dashboard
