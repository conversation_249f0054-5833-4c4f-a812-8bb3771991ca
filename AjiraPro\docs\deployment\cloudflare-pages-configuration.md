# Cloudflare Pages Configuration for AjiraPro

This document provides detailed information about the Cloudflare Pages configuration for the AjiraPro application.

## Project Configuration

- **Project Name**: ajirapro
- **Production Branch**: main
- **Framework Preset**: Create React App
- **Build Command**: `cd AjiraPro/frontend && npm install --legacy-peer-deps && npm run build`
- **Build Output Directory**: `AjiraPro/frontend/build`
- **Root Directory**: `/` (repository root)

## Environment Variables

### Production Environment

| Variable | Value | Description |
|----------|-------|-------------|
| REACT_APP_SUPABASE_URL | https://xmmbjopzpyotkyyfqzyq.supabase.co | Supabase project URL |
| REACT_APP_SUPABASE_ANON_KEY | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... | Supabase anonymous key |
| REACT_APP_API_URL | https://api.ajirapro.com | Backend API URL |
| NODE_VERSION | 18 | Node.js version |

### Preview Environment (for Pull Requests)

| Variable | Value | Description |
|----------|-------|-------------|
| REACT_APP_SUPABASE_URL | https://xmmbjopzpyotkyyfqzyq.supabase.co | Supabase project URL |
| REACT_APP_SUPABASE_ANON_KEY | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... | Supabase anonymous key |
| REACT_APP_API_URL | https://api-preview.ajirapro.com | Preview backend API URL |
| NODE_VERSION | 18 | Node.js version |

## Custom Domains

- **Primary Domain**: ajirapro.com
- **Additional Domains**: www.ajirapro.com

## Build Configuration Files

### _redirects

The `_redirects` file in the `public` directory configures routing for the single-page application:

```
/* /index.html 200
```

This ensures that all routes are handled by the React application.

### _headers

The `_headers` file in the `public` directory configures HTTP headers for security and caching:

```
# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://ajax.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.ajirapro.com;
  Strict-Transport-Security: max-age=********; includeSubDomains

# Cache static assets
/static/*
  Cache-Control: public, max-age=********, immutable

# Cache images
/*.ico
  Cache-Control: public, max-age=86400
/*.png
  Cache-Control: public, max-age=86400
/*.svg
  Cache-Control: public, max-age=86400
/*.jpg
  Cache-Control: public, max-age=86400
/*.jpeg
  Cache-Control: public, max-age=86400
/*.webp
  Cache-Control: public, max-age=86400

# Don't cache HTML
/*.html
  Cache-Control: public, max-age=0, must-revalidate
```

### _routes.json

The `_routes.json` file in the `public` directory configures routing for Cloudflare Pages:

```json
{
  "version": 1,
  "include": ["/*"],
  "exclude": [
    "/static/*",
    "/*.ico",
    "/*.png",
    "/*.svg",
    "/*.jpg",
    "/*.jpeg",
    "/*.webp",
    "/*.css",
    "/*.js",
    "/*.json"
  ]
}
```

## Deployment Settings

- **Automatic Deployments**: Enabled for the main branch
- **Preview Deployments**: Enabled for pull requests
- **Production Deployments**: Triggered by pushes to the main branch
- **Build Cache**: Enabled

## Analytics and Monitoring

- **Cloudflare Analytics**: Enabled
- **Error Tracking**: Enabled
- **Performance Monitoring**: Enabled

## Security Settings

- **SSL/TLS**: Managed by Cloudflare
- **Always Use HTTPS**: Enabled
- **Automatic HTTPS Rewrites**: Enabled
- **Opportunistic Encryption**: Enabled
- **TLS 1.3**: Enabled
- **HSTS**: Enabled

## Caching Configuration

- **Cache Control**: Configured via `_headers` file
- **Browser TTL**: Configured via `_headers` file
- **Edge Cache TTL**: Configured via `_headers` file

## Functions Configuration (Future)

- **Functions Directory**: `functions`
- **Functions Routes**: To be configured

## Deployment Process

1. Code is pushed to the main branch
2. GitHub Actions workflow is triggered
3. Frontend is built using the specified build command
4. Build output is deployed to Cloudflare Pages
5. Custom domain is automatically configured

## Rollback Process

1. Log in to Cloudflare dashboard
2. Navigate to Pages > ajirapro
3. Go to Deployments tab
4. Find the previous successful deployment
5. Click "Rollback to this deployment"

## Troubleshooting

### Common Build Issues

- **Node.js Version**: Ensure the Node.js version is set correctly
- **Dependency Issues**: Use `--legacy-peer-deps` flag with npm install
- **Environment Variables**: Verify environment variables are set correctly

### Common Runtime Issues

- **Routing Issues**: Check `_redirects` file configuration
- **CORS Issues**: Check `_headers` file configuration
- **API Connection Issues**: Verify API URL environment variable

### Checking Deployment Status

1. Log in to Cloudflare dashboard
2. Navigate to Pages > ajirapro
3. Go to Deployments tab
4. Check the status of the latest deployment

### Viewing Build Logs

1. Log in to Cloudflare dashboard
2. Navigate to Pages > ajirapro
3. Go to Deployments tab
4. Click on the deployment
5. Click "View build log"
