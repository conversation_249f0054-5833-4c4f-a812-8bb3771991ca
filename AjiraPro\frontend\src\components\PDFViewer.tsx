/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up the worker source with local URL to avoid CSP issues
pdfjs.GlobalWorkerOptions.workerSrc = `/pdf-worker/pdf.worker.min.js`;

// No need to cache resources as they're served locally

interface PDFViewerProps {
  className?: string;
  fileName?: string;
  resumeId?: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  className = '',
  fileName = 'resume.pdf',
  resumeId
}) => {
  const pdfBackendUrl = resumeId ? `/api/resumes/download/${resumeId}` : '';
  const { isDark } = useTheme();
  const [isDownloading, setIsDownloading] = useState(false);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to handle document load success
  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
    setIsLoading(false);
    setError(null);
  }

  // State to track loading progress
  const [loadingProgress, setLoadingProgress] = useState<number>(0);
  const [loadingStage, setLoadingStage] = useState<string>('Initializing...');
  const [retryCount, setRetryCount] = useState<number>(0);
  const MAX_RETRIES = 3;
  const TIMEOUT_DURATION = 5000; // 5 seconds timeout (reduced from 8)

    // Add a useEffect to validate and check the PDF URL
    // NOTE: Adding 'isLoading' and 'error' to dependencies as suggested by linter.
    // This might cause unintended re-renders if these states change within the effect.
    useEffect(() => {
      if (!pdfBackendUrl) {
        setError('No PDF URL provided.');
        setIsLoading(false);
        return;
      }

      // Reset states when URL changes
      setLoadingProgress(0);
      setLoadingStage('Validating URL...');
      setError(null);
      setIsLoading(true);

      // Check if the URL is valid
      try {
        // Validate URL format
        new URL(window.location.origin + pdfBackendUrl); // Use window.location.origin for full URL
        setLoadingProgress(10);
        setLoadingStage('Checking file accessibility...');

        // Try to fetch the PDF headers to check if it's accessible and is actually a PDF
        fetch(window.location.origin + pdfBackendUrl, {
          method: 'HEAD',
          // Add cache control to prevent caching issues
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
          .then(response => {
            setLoadingProgress(20);

            // Check if response is OK
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Check content type to ensure it's a PDF
            const contentType = response.headers.get('Content-Type');
            if (contentType && !contentType.includes('application/pdf') && !contentType.includes('octet-stream')) {
              throw new Error(`Invalid content type: ${contentType}. Expected PDF.`);
            }

            setLoadingStage('File accessible, preparing to render...');
            setLoadingProgress(30);

            // Set a timeout for the PDF loading process
            const timeoutId = setTimeout(() => {
              if (isLoading && !error) {
                // If we've tried multiple times, give up
                if (retryCount >= MAX_RETRIES) {
                  setError(`PDF loading timeout after ${MAX_RETRIES} attempts. The file might be too large or incompatible.`);
                  setIsLoading(false);
                } else {
                  // Otherwise, increment retry count and try again
                  setRetryCount(prev => prev + 1);
                  setLoadingStage(`Retrying (${retryCount + 1}/${MAX_RETRIES})...`);
                  // Force re-render by updating a state
                  setLoadingProgress(0);
                }
              }
            }, TIMEOUT_DURATION);

            return () => clearTimeout(timeoutId);
          })
          .catch(fetchError => {
            setError(`Error accessing PDF: ${fetchError.message}. Please try downloading instead.`);
            setIsLoading(false);
          });

      } catch (e) {
        // URL is invalid
        setError(`Invalid PDF URL format. Please try downloading instead.`);
        setIsLoading(false);
      }
    }, [pdfBackendUrl, retryCount]);

    const handleDownload = () => {
      setIsDownloading(true);

      // Create a hidden link and click it
      const link = document.createElement('a');
      link.href = pdfBackendUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Reset the downloading state after a short delay
      setTimeout(() => {
        setIsDownloading(false);
      }, 1000);
    };

  // Function to handle page navigation
  function changePage(offset: number) {
    if (pageNumber && numPages) {
      const newPageNumber = pageNumber + offset;
      if (newPageNumber >= 1 && newPageNumber <= numPages) {
        setPageNumber(newPageNumber);
      }
    }
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center w-full max-w-md px-4">
          <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>

          <p className="mb-2 font-medium">{loadingStage}</p>

          {/* Progress bar */}
          <div className={`w-full h-2 rounded-full ${isDark ? "bg-dark-300" : "bg-gray-200"} mb-2`}>
            <div
              className="h-2 rounded-full bg-blue-500 transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>

          <p className="text-sm text-gray-500">
            {retryCount > 0 ? `Retry attempt ${retryCount}/${MAX_RETRIES}` : 'Loading PDF preview...'}
          </p>

          {/* Cancel button */}
          <button
            onClick={() => {
              setIsLoading(false);
              setError('PDF loading cancelled by user.');
            }}
            className={`mt-4 px-3 py-1 rounded-md text-sm ${
              isDark ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  // Main PDF viewer with react-pdf
  if (!error) {
    return (
      <div className={`flex flex-col ${className}`}>
        {/* PDF Viewer using react-pdf */}
        <div className={`flex-1 overflow-auto ${isDark ? 'bg-dark-300' : 'bg-gray-100'} rounded-t-lg`}>
          <Document
            file={pdfBackendUrl}
            onLoadSuccess={(pdf) => {
              setLoadingProgress(100);
              onDocumentLoadSuccess(pdf);
            }}
            onLoadProgress={(progressData) => {
              // Update loading progress based on the loaded percentage
              const progress = Math.min(30 + (progressData.loaded / progressData.total) * 70, 99);
              setLoadingProgress(progress);
              setLoadingStage(`Loading PDF: ${Math.round(progress)}%`);
            }}
            onLoadError={(error) => {
              // Handle PDF loading error with detailed information
              let errorMessage = 'Unknown error';

              // Extract more specific error information
              if (error.message) {
                errorMessage = error.message;
              } else if (error.name === 'MissingPDFException') {
                errorMessage = 'The PDF file could not be found or is inaccessible';
              } else if (error.name === 'InvalidPDFException') {
                errorMessage = 'The PDF file is invalid or corrupted';
              } else if (error.name === 'PasswordException') {
                errorMessage = 'The PDF file is password protected';
              } else if (error.name === 'UnexpectedResponseException') {
                errorMessage = 'Unexpected server response while retrieving PDF';
              }

              setError(`Error loading PDF: ${errorMessage}. Please try downloading instead.`);
              setIsLoading(false);
            }}
            options={{
              cMapUrl: `/pdf-worker/cmaps/`,
              cMapPacked: true,
              standardFontDataUrl: `/pdf-worker/standard_fonts/`,
              disableStream: false,
              disableAutoFetch: false,
              isEvalSupported: true,
              isOffscreenCanvasSupported: true,
            }}
            loading={
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p>Loading PDF...</p>
                </div>
              </div>
            }
            className="mx-auto"
          >
            <Page
              pageNumber={pageNumber}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              className={`mx-auto shadow-lg ${isDark ? 'bg-dark-200' : 'bg-white'}`}
              scale={1.0}
              canvasBackground={isDark ? '#1f2937' : '#ffffff'}
              // Improve rendering quality
              width={Math.min(window.innerWidth - 80, 800)}
              renderMode="canvas"
              // Add event handlers for better debugging
              onRenderSuccess={() => {
                // Page rendered successfully
              }}
              onRenderError={() => {
                // Error rendering page
              }}
              loading={
                <div className="flex items-center justify-center h-[600px] w-full max-w-[600px]">
                  <div className="text-center">
                    <svg className="animate-spin w-8 h-8 mx-auto mb-2 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p>Loading page {pageNumber}...</p>
                  </div>
                </div>
              }
              error={
                <div className="flex items-center justify-center h-[600px] w-full max-w-[600px]">
                  <div className="text-center p-4">
                    <svg className="w-12 h-12 mx-auto mb-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-500 font-medium">Error loading page {pageNumber}</p>
                    <p className="mt-2 text-sm text-gray-500">Try navigating to a different page or refreshing the viewer</p>
                  </div>
                </div>
              }
            />
          </Document>
        </div>

        {/* Controls and navigation */}
        <div className={`p-3 flex justify-between items-center ${isDark ? 'bg-dark-400' : 'bg-gray-200'} rounded-b-lg`}>
          <div className="flex space-x-2">
            <button
              onClick={previousPage}
              disabled={pageNumber <= 1}
              className={`px-3 py-2 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              } ${pageNumber <= 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div className={`px-3 py-2 ${isDark ? 'text-white' : 'text-gray-800'}`}>
              Page {pageNumber} of {numPages}
            </div>

            <button
              onClick={nextPage}
              disabled={numPages !== null && pageNumber >= numPages}
              className={`px-3 py-2 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              } ${numPages !== null && pageNumber >= numPages ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className={`px-4 py-2 rounded-md font-medium flex items-center justify-center ${
              isDark
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } ${isDownloading ? 'opacity-75 cursor-not-allowed' : ''}`}
          >
            {isDownloading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Downloading...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                Download Resume
              </>
            )}
          </button>
        </div>
      </div>
    );
  }

  // Fallback UI if PDF.js fails to load the PDF
  return (
    <div className={`flex flex-col ${className}`}>
      {/* PDF Preview Placeholder */}
      <div className={`flex-1 flex items-center justify-center ${isDark ? 'bg-dark-300 text-white' : 'bg-gray-100 text-gray-800'} rounded-t-lg`}>
        <div className="text-center p-8 max-w-md">
          <svg className={`w-20 h-20 mx-auto mb-6 ${isDark ? 'text-gray-500' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>

          <h3 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            PDF Preview Unavailable
          </h3>

          <p className="mb-6">
            {error || "We couldn't display the PDF directly in the browser. Please use the download button below to view your resume."}
          </p>

          <div className="mb-6 text-sm text-left p-4 bg-gray-800 text-gray-300 rounded overflow-auto max-h-48">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium">Diagnostic Information</h4>
              <button
                onClick={() => {
                  // Copy diagnostic info to clipboard
                  const diagnosticInfo = `
PDF Viewer Error Report
-----------------------
Error: ${error}
URL: ${pdfBackendUrl || 'No URL provided'}
Browser: ${navigator.userAgent}
Time: ${new Date().toLocaleString()}
Retry Count: ${retryCount}
File Path: ${pdfBackendUrl ? new URL(window.location.origin + pdfBackendUrl).pathname.split('/').pop() : 'Unknown'}
                  `.trim();

                  navigator.clipboard.writeText(diagnosticInfo)
                    .then(() => alert('Diagnostic information copied to clipboard'))
                    .catch(() => alert('Failed to copy diagnostic info'));
                }}
                className="text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded"
              >
                Copy Info
              </button>
            </div>
            <p><strong>URL:</strong> {pdfBackendUrl || 'No URL provided'}</p>
            <p className="mt-2"><strong>File Name:</strong> {pdfBackendUrl ? new URL(window.location.origin + pdfBackendUrl).pathname.split('/').pop() : 'Unknown'}</p>
            <p className="mt-2"><strong>Browser:</strong> {navigator.userAgent}</p>
            <p className="mt-2"><strong>Time:</strong> ${new Date().toLocaleString()}</p>
            <p className="mt-2"><strong>Retry Attempts:</strong> ${retryCount}</p>
          </div>

          <div className="flex flex-col space-y-4">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className={`px-4 py-3 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              } ${isDownloading ? 'opacity-75 cursor-not-allowed' : ''}`}
            >
              {isDownloading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Downloading...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download Resume (Method 1)
                </>
              )}
            </button>

            <a
              href={pdfBackendUrl}
              target="_blank"
              rel="noopener noreferrer"
              className={`px-4 py-3 rounded-md font-medium text-center ${
                isDark
                  ? 'bg-gray-700 hover:bg-gray-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
              }`}
            >
              Open in New Tab
            </a>

            {/* Retry button for PDF preview */}
            <button
              onClick={() => {
                // Reset all states
                setIsLoading(true);
                setError(null);
                setNumPages(null);
                setPageNumber(1);
                setRetryCount(0);
                setLoadingProgress(0);
                setLoadingStage('Initializing retry...');

                // Force browser to refetch the PDF by adding a cache-busting parameter
                const url = new URL(window.location.origin + pdfBackendUrl);
                url.searchParams.set('t', Date.now().toString());

                // Create a new Image element to preload and test the URL
                const img = new Image();
                img.onload = () => {
                  // Successfully preloaded resource
                };
                img.onerror = () => {
                  // Failed to preload resource
                };
                img.src = url.toString();
              }}
              className={`px-4 py-3 rounded-md font-medium flex items-center justify-center ${
                isDark
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              }`}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Retry PDF Preview
            </button>
          </div>
        </div>
      </div>

      {/* Info bar at the bottom */}
      <div className={`p-3 text-center text-sm ${isDark ? 'bg-dark-400' : 'bg-gray-200'} text-gray-600 rounded-b-lg`}>
        <p>Your resume has been successfully uploaded and analyzed.</p>
        <p className="mt-1 text-xs">
          {isDark ? (
            <span className="text-yellow-400">Note: PDF preview may be temporarily unavailable. Please try again later or use the download options.</span>
          ) : (
            <span className="text-yellow-600">Note: PDF preview may be temporarily unavailable. Please try again later or use the download options.</span>
          )}
        </p>
      </div>
    </div>
  );
};

export default PDFViewer;
