from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from uuid import UUID

class TemplateResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    type: str = Field(..., description="Type of template: resume or cv")
    category: Optional[str] = None
    thumbnail_url: str
    preview_url: str
    is_premium: bool = False
    is_featured: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
