#!/usr/bin/env python3
"""
Simple test script to check if our FastAPI app can start
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing imports...")
    
    # Test basic imports
    from fastapi import FastAPI
    print("✓ FastAPI imported successfully")
    
    from fastapi.middleware.cors import CORSMiddleware
    print("✓ CORSMiddleware imported successfully")
    
    # Test our app imports
    from app.core.config import settings
    print("✓ Settings imported successfully")
    print(f"✓ CORS Origins: {settings.CORS_ORIGINS}")
    
    from app.main import app
    print("✓ Main app imported successfully")
    
    print("\n🎉 All imports successful! The app should start correctly.")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
