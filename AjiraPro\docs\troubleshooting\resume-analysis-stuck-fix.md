# Resume Analysis Stuck at "Preparing to analyze your document..." - Fix Guide

## Problem Summary

The resume optimization flow gets stuck at "Preparing to analyze your document..." for about 10 minutes, then times out. Users cannot proceed with resume analysis.

## Root Cause Analysis

### Issue Identified: Missing Celery Worker Service

The problem is that **the Celery worker is not running in production**. The analysis shows:

✅ **Working Components:**
- FastAPI server starts successfully
- Redis connection is configured
- Tasks are being queued (task_result.id is generated)
- Supabase database connection works

❌ **Missing Component:**
- **Celery worker service is not processing tasks**

### Evidence from Logs:
- Only FastAPI server startup logs are present
- No Celery worker logs in the output
- Health check shows "unhealthy: no workers responded"
- Tasks are queued but never processed

## Resume Optimization Flow (Current Implementation)

### 1. Frontend Flow (`ResumeOptimizerPage.tsx`):
```
User uploads file → validation → authentication check
↓
uploadResume() → creates record in Supabase  
↓
startResumeAnalysis() → triggers backend analysis
↓
Redirects to /resume-results?id={resumeId}
```

### 2. Backend Analysis Trigger (`resume_analysis.py`):
```
POST /api/resumes/{resume_id}/analyze
↓
Queues analyze_resume_task.delay(str(resume_id))
↓
Returns 202 status with task_id
```

### 3. Celery Task Chain (`tasks/resume_analysis.py`):
```
analyze_resume_task → parse_resume_task → score_format_task
↓
Each task updates database status fields
↓
Uses Claude for parsing and format scoring
```

### 4. Status Monitoring:
```
Frontend polls GET /api/resumes/{resume_id}/status
↓
Returns validation_status, parsing_status, scoring_status
```

## Railway Deployment Issue

According to the documentation, Railway should have **3 separate services**:

1. **FastAPI Service** ✅ (Running)
2. **Redis Service** ✅ (Running)  
3. **Celery Worker Service** ❌ (Missing/Not Running)

### Current Railway Configuration:
- Only running `python start.py` (FastAPI server)
- Missing separate Celery worker service

## Solutions

### Solution 1: Add Separate Celery Worker Service (Recommended)

1. **Go to Railway Dashboard**
2. **Create new service** from same GitHub repository
3. **Configure service:**
   - Root Directory: `AjiraPro/backend/`
   - Start Command: `/app/worker.sh`
4. **Copy all environment variables** from FastAPI service
5. **Deploy the service**

### Solution 2: Temporary Combined Service (Quick Fix)

I've created `start_combined.py` that runs both FastAPI and Celery worker in the same container:

**Changes Made:**
- Created `backend/start_combined.py` - Combined startup script
- Updated `backend/railway.toml` - Changed startCommand to use combined script
- Added environment variable validation
- Added error handling and logging

**To Deploy:**
1. Commit and push changes to develop branch
2. Railway will automatically redeploy with the combined service

## Required Environment Variables

The Celery worker needs these critical environment variables:

### Core Infrastructure:
- `SUPABASE_URL` ✅
- `SUPABASE_KEY` ✅  
- `REDIS_URL` ✅

### AI Services (Critical):
- `CLAUDE_API_KEY` ❓ (Required for parsing)
- `OPENAI_API_KEY` ❓ (Required for content scoring)
- `GEMINI_API_KEY` ❓ (Required for validation)

### Application Settings:
- `ENVIRONMENT=production` ✅
- `DEBUG=False` ✅

## Testing the Fix

### 1. Test Celery Connection:
```bash
cd AjiraPro/backend
python test_celery.py
```

### 2. Check Health Endpoint:
```
GET https://fajirapro-production.up.railway.app/health
```

Should show:
```json
{
  "redis_celery": "healthy",
  "ai_services": {
    "openai": "healthy",
    "claude": "healthy"
  }
}
```

### 3. Test Resume Analysis:
1. Upload a resume through the frontend
2. Check that it progresses beyond "Preparing to analyze..."
3. Verify status updates in real-time

## Next Steps

1. **Immediate:** Deploy the combined service fix
2. **Short-term:** Verify all AI API keys are configured
3. **Long-term:** Set up proper separate Celery worker service
4. **Monitoring:** Add better logging and error handling

## Prevention

- Set up monitoring for Celery worker health
- Add alerts for task queue backlog
- Regular health checks for all services
- Proper error handling and user feedback
