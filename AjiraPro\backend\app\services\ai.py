"""
AI Service Integration Module

This module provides a unified interface for interacting with AI services
(OpenAI and Claude) for resume analysis and scoring.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..core.config import settings

logger = logging.getLogger(__name__)

class AIServiceError(Exception):
    """Base exception for AI service errors"""
    pass

class AIRateLimitError(AIServiceError):
    """Raised when AI service rate limit is exceeded"""
    pass

class AIServiceUnavailableError(AIServiceError):
    """Raised when AI service is unavailable"""
    pass

class AIService:
    """Unified AI service interface for OpenAI and Claude"""
    
    def __init__(self):
        self.openai_headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        self.claude_headers = {
            "x-api-key": settings.CLAUDE_API_KEY,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        self.timeout = settings.AI_REQUEST_TIMEOUT
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, AIRateLimitError))
    )
    async def call_openai(self, messages: List[Dict[str, str]], model: str = None) -> Dict[str, Any]:
        """
        Make a request to OpenAI API with retry logic
        
        Args:
            messages: List of message objects for the chat completion
            model: OpenAI model to use (defaults to configured model)
            
        Returns:
            Dict containing the API response
            
        Raises:
            AIServiceError: If the API call fails
        """
        if not settings.OPENAI_API_KEY:
            raise AIServiceError("OpenAI API key not configured")
        
        model = model or settings.OPENAI_MODEL
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 2000
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=self.openai_headers,
                    json=payload
                )
                
                if response.status_code == 429:
                    raise AIRateLimitError("OpenAI rate limit exceeded")
                elif response.status_code >= 500:
                    raise AIServiceUnavailableError(f"OpenAI service unavailable: {response.status_code}")
                elif response.status_code != 200:
                    raise AIServiceError(f"OpenAI API error: {response.status_code} - {response.text}")
                
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"OpenAI request error: {str(e)}")
            raise AIServiceError(f"Failed to connect to OpenAI: {str(e)}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, AIRateLimitError))
    )
    async def call_claude(self, messages: List[Dict[str, str]], model: str = None) -> Dict[str, Any]:
        """
        Make a request to Claude API with retry logic
        
        Args:
            messages: List of message objects for the chat completion
            model: Claude model to use (defaults to configured model)
            
        Returns:
            Dict containing the API response
            
        Raises:
            AIServiceError: If the API call fails
        """
        if not settings.CLAUDE_API_KEY:
            raise AIServiceError("Claude API key not configured")
        
        model = model or settings.CLAUDE_MODEL
        
        # Convert messages to Claude format
        system_message = ""
        user_messages = []
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                user_messages.append(msg)
        
        payload = {
            "model": model,
            "max_tokens": 2000,
            "temperature": 0.3,
            "messages": user_messages
        }
        
        if system_message:
            payload["system"] = system_message
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    "https://api.anthropic.com/v1/messages",
                    headers=self.claude_headers,
                    json=payload
                )
                
                if response.status_code == 429:
                    raise AIRateLimitError("Claude rate limit exceeded")
                elif response.status_code >= 500:
                    raise AIServiceUnavailableError(f"Claude service unavailable: {response.status_code}")
                elif response.status_code != 200:
                    raise AIServiceError(f"Claude API error: {response.status_code} - {response.text}")
                
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"Claude request error: {str(e)}")
            raise AIServiceError(f"Failed to connect to Claude: {str(e)}")
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check the health of both AI services
        
        Returns:
            Dict with service availability status
        """
        health_status = {
            "openai": False,
            "claude": False
        }
        
        # Test OpenAI
        try:
            if settings.OPENAI_API_KEY:
                test_messages = [{"role": "user", "content": "Hello"}]
                await self.call_openai(test_messages)
                health_status["openai"] = True
        except Exception as e:
            logger.warning(f"OpenAI health check failed: {str(e)}")
        
        # Test Claude
        try:
            if settings.CLAUDE_API_KEY:
                test_messages = [{"role": "user", "content": "Hello"}]
                await self.call_claude(test_messages)
                health_status["claude"] = True
        except Exception as e:
            logger.warning(f"Claude health check failed: {str(e)}")
        
        return health_status
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """
        Validate that API keys are configured
        
        Returns:
            Dict with API key configuration status
        """
        return {
            "openai_configured": bool(settings.OPENAI_API_KEY),
            "claude_configured": bool(settings.CLAUDE_API_KEY)
        }

# Global AI service instance
ai_service = AIService()

async def get_ai_service() -> AIService:
    """Dependency injection for AI service"""
    return ai_service
