import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../utils/supabase';
import { Session, User } from '@supabase/supabase-js';

// Define the shape of our context
interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData?: { full_name?: string }) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  refreshProfile: () => Promise<void>;
  isAuthenticated: boolean;
  checkUserExists: (email: string) => Promise<{ exists: boolean; error: any }>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  signInWithMagicLink: (email: string) => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ data?: any; error?: any }>;
}

// Define the shape of our user profile
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  phone_number?: string;
  bio?: string;
  created_at: string;
  updated_at?: string;
  has_completed_onboarding?: boolean;
  metadata?: {
    professional_field?: string;
    experience_level?: string;
    job_search_status?: string;
    [key: string]: any; // Allow for additional metadata fields
  };
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  profile: null,
  isLoading: true,
  signIn: async () => ({ error: null }),
  signUp: async () => ({ error: null }),
  signOut: async () => ({ error: null }),
  updateProfile: async () => ({ error: null }),
  refreshProfile: async () => {},
  isAuthenticated: false,
  checkUserExists: async () => ({ exists: false, error: null }),
  resetPassword: async () => ({ error: null }),
  signInWithMagicLink: async () => ({ error: null }),
  signInWithGoogle: async () => ({ data: null, error: null }),
});

// Props for the AuthProvider component
interface AuthProviderProps {
  children: ReactNode;
}

// Create the AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Initialize auth state
  useEffect(() => {
    // Set initial session and user
    const initializeAuth = async () => {
      setIsLoading(true);

      // Get current session
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);

      if (session?.user) {
        setUser(session.user);
        await fetchProfile(session.user.id);
      }

      setIsLoading(false);
    };

    initializeAuth();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setProfile(null);
        }
      }
    );

    // Clean up subscription on unmount
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  // Fetch user profile from Supabase
  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        // If there's an error fetching the profile, we can't set it
        return;
      }

      setProfile(data as UserProfile);
    } catch (error) {
      // Silent fail - we'll just have a null profile
    }
  };

  // Refresh the user profile
  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, userData?: { full_name?: string }) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Update user profile
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('User not authenticated') };
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);

      if (!error) {
        await refreshProfile();
      }

      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Check if a user with the given email exists without sending an email
  const checkUserExists = async (email: string) => {
    try {
      // Try to sign in with a fake password - this won't send an email
      // but will tell us if the user exists
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password: 'fake-password-that-wont-work-' + Math.random(),
      });

      // If the error message indicates the user doesn't exist, return false
      if (signInError) {
        const errorMsg = signInError.message.toLowerCase();
        if (
          errorMsg.includes("user not found") ||
          errorMsg.includes("user does not exist") ||
          errorMsg.includes("invalid user")
        ) {
          return { exists: false, error: null };
        }

        // If we get an invalid credentials error, the user exists
        if (errorMsg.includes("invalid login credentials")) {
          return { exists: true, error: null };
        }

        // For other errors, we can't determine if the user exists
        return { exists: false, error: signInError };
      }

      // If there's no error (which shouldn't happen with a fake password),
      // assume the user exists
      return { exists: true, error: null };
    } catch (error) {
      // If there's an exception, we can't determine if the user exists
      return { exists: false, error };
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      // Use absolute URL to ensure proper redirection
      // Don't use query parameters as they might interfere with Supabase's own parameters
      const redirectUrl = `https://ajirapro.com/reset-password`;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Sign in with magic link (passwordless)
  const signInWithMagicLink = async (email: string) => {
    try {
      // Use absolute URL to ensure proper redirection
      const redirectUrl = 'https://ajirapro.com/auth/magic-link';

      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: redirectUrl,
        }
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      // Use absolute URL to ensure proper redirection
      const redirectUrl = 'https://ajirapro.com/auth/google-callback';

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          // You can specify additional scopes if needed
          scopes: 'email profile',
        }
      });

      return { data, error };
    } catch (error) {
      return { error };
    }
  };

  // Determine if user is authenticated
  const isAuthenticated = !!user && !!session;

  // Create the context value
  const value = {
    session,
    user,
    profile,
    isLoading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshProfile,
    isAuthenticated,
    checkUserExists,
    resetPassword,
    signInWithMagicLink,
    signInWithGoogle,
  };

  // Provide the context to children
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Create a hook for using the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
