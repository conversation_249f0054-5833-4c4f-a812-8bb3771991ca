name: Main Workflow

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Check repository structure
        run: |
          echo "Repository structure:"
          find . -type d -not -path "*/\.*" | sort

      - name: Check frontend directory
        run: |
          echo "Frontend directory structure:"
          find ./AjiraPro/frontend -type f -not -path "*/node_modules/*" -not -path "*/\.*" | sort || echo "Frontend directory not found"

      - name: Check backend directory
        run: |
          echo "Backend directory structure:"
          find ./AjiraPro/backend -type f -not -path "*/node_modules/*" -not -path "*/\.*" | sort || echo "Backend directory not found"
