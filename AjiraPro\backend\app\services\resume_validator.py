"""
AI-Based Resume Validator Service

Uses Google Gemini 1.5 Flash to quickly determine if a document is a resume
by directly processing the file without needing full text extraction.
"""

import json
import logging
import base64
from typing import Dict, Any

import google.generativeai as genai
from ..core.config import settings

logger = logging.getLogger(__name__)

class ResumeValidationError(Exception):
    """Exception raised when resume validation fails"""
    pass

class GeminiResumeValidator:
    """Service for validating resumes using Google Gemini 1.5 Flash"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = settings.GEMINI_MODEL

        # Configure Gemini API
        if settings.GEMINI_API_KEY:
            genai.configure(api_key=settings.GEMINI_API_KEY)
            self.gemini_model = genai.GenerativeModel(self.model)
        else:
            raise ResumeValidationError("GEMINI_API_KEY not configured")

    async def validate_document_as_resume(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Use Gemini 1.5 Flash to quickly determine if a document is a resume

        Args:
            file_content: Raw file content as bytes
            filename: Original filename

        Returns:
            Dict with validation results

        Raises:
            ResumeValidationError: If validation fails
        """
        try:
            # Check if file is empty
            if not file_content or len(file_content) < 100:
                return {
                    "is_resume": False,
                    "likelihood": 0,
                    "reason": "Document appears to be empty or too small",
                    "confidence": "high"
                }

            # Encode file as base64 for Gemini
            file_base64 = base64.b64encode(file_content).decode('utf-8')

            # Determine file type
            file_extension = filename.lower().split('.')[-1] if '.' in filename else 'unknown'

            # Create validation prompt for Gemini
            validation_prompt = self._create_gemini_validation_prompt(file_base64, filename, file_extension)

            # Call Gemini for validation
            response = self.gemini_model.generate_content(
                contents=[{"text": validation_prompt}],
                generation_config={
                    "response_mime_type": "application/json",  # Force JSON output
                    "temperature": 0.1,  # Low temperature for consistent results
                    "max_output_tokens": 200  # Keep response concise
                }
            )

            # Extract and parse response
            if response and response.text:
                validation_result = response.text.strip()

                # Parse JSON response
                try:
                    result_data = json.loads(validation_result)
                    return self._structure_validation_result(result_data)
                except json.JSONDecodeError as e:
                    self.logger.error(f"Failed to parse Gemini validation response: {str(e)}")
                    self.logger.error(f"Raw response: {validation_result}")
                    raise ResumeValidationError(f"Invalid JSON response from Gemini: {str(e)}")
            else:
                raise ResumeValidationError("Empty response from Gemini")

        except Exception as e:
            self.logger.error(f"Gemini API error during validation: {str(e)}")
            raise ResumeValidationError(f"Validation failed: {str(e)}")

    def _create_gemini_validation_prompt(self, file_base64: str, filename: str, file_extension: str) -> str:
        """Create prompt for Gemini resume validation"""
        return f"""
Analyze the attached document to determine if it's a resume/CV.

FILENAME: {filename}
FILE TYPE: {file_extension.upper()}

This is a {file_extension.upper()} file encoded in base64. Analyze its content to determine if it's a resume/CV based on:

1. Presence of personal information (name, contact details)
2. Work experience or employment history sections
3. Education background
4. Skills or qualifications
5. Professional summary or objective
6. Overall structure and format typical of resumes

Respond with this exact JSON format:
{{
  "is_resume": true,
  "likelihood": 95,
  "reason": "Contains personal info, work experience, education, and skills",
  "confidence": "high"
}}

IMPORTANT:
- likelihood should be 0-100 (percentage)
- is_resume should be true if likelihood >= 70
- confidence should be "high", "medium", or "low"
- reason should be concise (max 100 characters)
- Return only valid JSON, no additional text

File data (base64): {file_base64}
"""

    def _structure_validation_result(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Structure validation result into standardized format"""
        is_resume = data.get("is_resume", False)
        likelihood = int(data.get("likelihood", 0))
        reason = data.get("reason", "Unknown")
        confidence = data.get("confidence", "low")

        # Ensure consistency
        if likelihood >= 70 and not is_resume:
            is_resume = True
        elif likelihood < 70 and is_resume:
            is_resume = False

        return {
            "is_resume": is_resume,
            "likelihood": likelihood,
            "reason": reason,
            "confidence": confidence,
            "validation_method": "gemini_1_5_flash"
        }

# Global Gemini resume validator instance (lazy-loaded)
_ai_resume_validator = None

def get_ai_resume_validator() -> GeminiResumeValidator:
    """Dependency injection for Gemini resume validator (lazy-loaded)"""
    global _ai_resume_validator
    if _ai_resume_validator is None:
        _ai_resume_validator = GeminiResumeValidator()
    return _ai_resume_validator

# For backward compatibility
ai_resume_validator = get_ai_resume_validator()
