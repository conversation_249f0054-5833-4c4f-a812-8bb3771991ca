# AjiraPro Infrastructure Documentation

This document provides an overview of the infrastructure used for the AjiraPro application.

## Architecture Overview

AjiraPro follows a modern cloud-native architecture with the following components:

```
                                  ┌─────────────────┐
                                  │                 │
                                  │  Cloudflare     │
                                  │  (DNS, CDN)     │
                                  │                 │
                                  └────────┬────────┘
                                           │
                                           ▼
┌─────────────────┐              ┌─────────────────┐
│                 │              │                 │
│  Cloudflare     │◄────────────►│  React Frontend │
│  Pages          │              │  (TypeScript)   │
│                 │              │                 │
└────────┬────────┘              └─────────────────┘
         │
         │
         ▼
┌─────────────────┐              ┌─────────────────┐
│                 │              │                 │
│  Railway        │◄────────────►│  FastAPI Backend│
│  (Backend Host) │              │  (Python)       │
│                 │              │                 │
└────────┬────────┘              └─────────────────┘
         │
         │
         ▼
┌─────────────────┐              ┌─────────────────┐
│                 │              │                 │
│  Supabase       │◄────────────►│  PostgreSQL DB  │
│  (Backend)      │              │  Auth, Storage  │
│                 │              │                 │
└─────────────────┘              └─────────────────┘
```

## Infrastructure Components

### Frontend Infrastructure

- **Cloudflare Pages**: Hosts the React frontend application
  - URL: https://ajirapro.com
  - Build command: `cd AjiraPro/frontend && npm install --legacy-peer-deps && npm run build`
  - Build output directory: `AjiraPro/frontend/build`
  - Automatic deployments on push to main branch

- **Cloudflare DNS**: Manages DNS records for ajirapro.com
  - A/AAAA records for apex domain
  - CNAME for www subdomain pointing to Cloudflare Pages

### Backend Infrastructure

- **Railway**: Hosts the FastAPI backend application (to be set up)
  - Automatic deployments from GitHub
  - Environment variables managed through Railway dashboard
  - Scaling handled automatically

- **Supabase**: Provides database, authentication, and storage
  - PostgreSQL database for application data
  - Auth service for user authentication
  - Storage buckets for file storage
  - Row-level security policies for data protection

### CI/CD Infrastructure

- **GitHub Actions**: Automates testing and deployment
  - Frontend workflow: `.github/workflows/frontend-ci-cd.yml`
  - Backend workflow: `.github/workflows/backend-ci-cd.yml`
  - Triggered on push to main branch

## Environment Variables

### Frontend Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| REACT_APP_SUPABASE_URL | Supabase project URL | Yes |
| REACT_APP_SUPABASE_ANON_KEY | Supabase anonymous key | Yes |
| REACT_APP_API_URL | Backend API URL | Yes |

### Backend Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| SUPABASE_URL | Supabase project URL | Yes |
| SUPABASE_SERVICE_KEY | Supabase service key | Yes |
| OPENAI_API_KEY | OpenAI API key | Yes |
| FLUTTERWAVE_SECRET_KEY | Flutterwave secret key | For production |
| FLUTTERWAVE_PUBLIC_KEY | Flutterwave public key | For production |

## Security Considerations

- **SSL/TLS**: All communications are encrypted using HTTPS
- **Authentication**: JWT-based authentication through Supabase Auth
- **Authorization**: Row-level security policies in Supabase
- **API Security**: Rate limiting and input validation
- **Environment Variables**: Sensitive information stored as environment variables, not in code

## Monitoring and Logging

- **Cloudflare Analytics**: Frontend performance and traffic monitoring
- **Supabase Logflare**: Database and auth logging (to be set up)
- **Application Logs**: Structured logging in the backend application

## Backup and Disaster Recovery

- **Database Backups**: Automated daily backups through Supabase
- **Code Repository**: All code is version-controlled in GitHub
- **Infrastructure as Code**: Infrastructure configurations are documented and version-controlled

## Scaling Considerations

- **Frontend Scaling**: Cloudflare Pages automatically scales to handle traffic
- **Backend Scaling**: Railway automatically scales based on demand
- **Database Scaling**: Supabase provides scaling options as needed

## Future Infrastructure Improvements

- Implement Redis for caching and session management
- Set up Celery for background task processing
- Implement comprehensive monitoring and alerting
- Set up staging environments for testing before production deployment
