import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

interface CardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  hover?: boolean;
  className?: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  icon,
  hover = false,
  className = '',
  onClick,
}) => {
  const { isDark } = useTheme();

  const baseClasses = `rounded-lg overflow-hidden ${
    isDark
      ? 'bg-dark-200 border border-dark-300 shadow-[0_4px_20px_rgba(0,0,0,0.3)]'
      : 'bg-white border border-gray-200 shadow-md'
  } transition-all duration-300`;

  const hoverClasses = hover
    ? isDark
      ? 'hover:shadow-[0_8px_30px_rgba(0,255,255,0.2)] hover:border-neon-cyan cursor-pointer'
      : 'hover:shadow-lg hover:border-blue-300 cursor-pointer'
    : '';

  return (
    <motion.div
      className={`${baseClasses} ${hoverClasses} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={hover ? { y: -5 } : {}}
      onClick={onClick}
    >
      {(title || icon) && (
        <div className={`px-6 py-4 border-b ${isDark ? 'border-dark-400' : 'border-gray-200'}`}>
          <div className="flex items-center">
            {icon && <div className="mr-3">{icon}</div>}
            <div>
              {title && (
                <h3 className={`text-lg font-semibold ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  {subtitle}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="p-6">{children}</div>
    </motion.div>
  );
};

export default Card;
