import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

interface ButtonProps extends Omit<HTMLMotionProps<"button">, "whileHover" | "whileTap" | "children"> {
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  className = '',
  disabled,
  ...props
}) => {
  const { isDark } = useTheme();

  // Base classes
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium focus:outline-none transition-all duration-200';

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  // Variant classes based on theme
  const variantClasses = {
    primary: isDark
      ? 'bg-dark-300 text-neon-cyan border border-neon-cyan hover:bg-dark-400 active:bg-dark-500 shadow-[0_0_10px_rgba(0,255,255,0.3)]'
      : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md',
    secondary: isDark
      ? 'bg-dark-400 text-neon-magenta border border-neon-magenta hover:bg-dark-500 active:bg-dark-300 shadow-[0_0_10px_rgba(255,0,255,0.3)]'
      : 'bg-gray-200 text-gray-800 hover:bg-gray-300 active:bg-gray-400',
    outline: isDark
      ? 'bg-transparent border border-neon-blue text-neon-blue hover:bg-dark-300 active:bg-dark-400'
      : 'bg-transparent border border-blue-600 text-blue-600 hover:bg-blue-50 active:bg-blue-100',
    text: isDark
      ? 'bg-transparent text-neon-cyan hover:text-neon-blue active:text-neon-magenta'
      : 'bg-transparent text-blue-600 hover:text-blue-700 active:text-blue-800',
  };

  // Width classes
  const widthClasses = fullWidth ? 'w-full' : '';

  // Disabled classes
  const disabledClasses = (disabled || isLoading)
    ? 'opacity-50 cursor-not-allowed'
    : 'cursor-pointer';

  // Combine all classes
  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClasses} ${disabledClasses} ${className}`;

  return (
    <motion.button
      className={buttonClasses}
      disabled={disabled || isLoading}
      whileHover={!disabled && !isLoading ? { scale: 1.03 } : {}}
      whileTap={!disabled && !isLoading ? { scale: 0.97 } : {}}
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}

      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      <span>{children}</span>
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </motion.button>
  );
};

export default Button;
