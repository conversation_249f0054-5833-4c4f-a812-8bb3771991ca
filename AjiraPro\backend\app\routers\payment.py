from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import List
from ..models.payment import PaymentCreate, PaymentResponse, PaymentVerify
from ..services.payment import PaymentService
from ..core.security import get_current_user
from ..models.user import User

router = APIRouter(
    prefix="/payments",
    tags=["payments"],
    responses={404: {"description": "Not found"}},
)

@router.post("/initiate", response_model=dict)
async def initiate_payment(
    payment: PaymentCreate,
    current_user: User = Depends(get_current_user),
    payment_service: PaymentService = Depends(),
):
    """
    Initiate a payment transaction with Flutterwave.
    """
    payment_link = await payment_service.initiate_payment(payment, current_user)
    if not payment_link:
        raise HTTPException(status_code=500, detail="Failed to initiate payment")
    return {"payment_link": payment_link}


@router.post("/verify", response_model=PaymentResponse)
async def verify_payment(
    payment: PaymentVerify,
    current_user: User = Depends(get_current_user),
    payment_service: PaymentService = Depends(),
):
    """
    Verify a payment transaction.
    """
    verified_payment = await payment_service.verify_payment(payment.transaction_id, current_user.id)
    if not verified_payment:
        raise HTTPException(status_code=400, detail="Payment verification failed")
    return verified_payment


@router.get("/", response_model=List[PaymentResponse])
async def get_payments(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    payment_service: PaymentService = Depends(),
):
    """
    Get all payments for the current user.
    """
    return await payment_service.get_payments(current_user.id, skip, limit)


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: str,
    current_user: User = Depends(get_current_user),
    payment_service: PaymentService = Depends(),
):
    """
    Get a specific payment by ID.
    """
    payment = await payment_service.get_payment(payment_id, current_user.id)
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    return payment


@router.post("/webhook", status_code=status.HTTP_200_OK)
async def payment_webhook(
    request: Request,
    payment_service: PaymentService = Depends(),
):
    """
    Webhook endpoint for Flutterwave payment notifications.
    """
    payload = await request.json()
    await payment_service.process_webhook(payload)
    return {"status": "success"}
