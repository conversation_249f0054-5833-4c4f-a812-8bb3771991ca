import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const RegisterPage: React.FC = () => {
  const { isDark } = useTheme();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [magicLinkLoading, setMagicLinkLoading] = useState(false);
  const [resetPasswordLoading, setResetPasswordLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [existingEmail, setExistingEmail] = useState<string | null>(null);
  const [showExistingEmailOptions, setShowExistingEmailOptions] = useState(false);
  const navigate = useNavigate();
  const { signUp, isAuthenticated, checkUserExists, resetPassword, signInWithMagicLink, signInWithGoogle } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSendMagicLink = async () => {
    if (!existingEmail) return;

    setMagicLinkLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await signInWithMagicLink(existingEmail);

      if (error) {
        throw error;
      }

      setSuccess('Magic link sent! Please check your email to sign in.');
      setShowExistingEmailOptions(false);

      // Redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'An error occurred while sending the magic link');
    } finally {
      setMagicLinkLoading(false);
    }
  };

  const handleCancelExistingEmailOptions = () => {
    setShowExistingEmailOptions(false);
    setExistingEmail(null);
    setEmail('');
  };

  const handleGoogleSignUp = async () => {
    try {
      setGoogleLoading(true);
      setError(null);

      const { error } = await signInWithGoogle();

      if (error) {
        throw error;
      }

      // The redirect will be handled by the OAuth provider
      // and the GoogleCallback component
    } catch (error: any) {
      setError(error.message || 'An error occurred during Google sign up');
      setGoogleLoading(false);
    }
  };

  const handleSendPasswordReset = async () => {
    if (!existingEmail) return;

    setResetPasswordLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await resetPassword(existingEmail);

      if (error) {
        throw error;
      }

      setSuccess('Password reset instructions have been sent to your email.');
      setShowExistingEmailOptions(false);

      // Redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'An error occurred while sending the reset instructions');
    } finally {
      setResetPasswordLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      setLoading(false);
      return;
    }

    try {
      // Check if user already exists
      const { exists, error: checkError } = await checkUserExists(email);

      if (checkError) {
        // Continue with registration attempt even if check fails
      } else if (exists) {
        // Instead of showing an error, show options for existing email
        setExistingEmail(email);
        setShowExistingEmailOptions(true);
        setLoading(false);
        return;
      }

      // Proceed with registration
      const { error } = await signUp(email, password, { full_name: fullName });

      if (error) {
        // If the error message indicates the user already exists, provide a clearer message
        if (error.message && (
            error.message.includes('already registered') ||
            error.message.includes('already exists') ||
            error.message.includes('already taken')
          )) {
          throw new Error('An account with this email already exists. Please sign in or reset your password.');
        }
        throw error;
      }

      setSuccess('Registration successful! Please check your email for verification.');

      // Redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} flex flex-col justify-center py-12 sm:px-6 lg:px-8`}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className={`mt-6 text-center text-3xl font-extrabold ${isDark ? 'text-white' : 'text-gray-900'}`}>
          {showExistingEmailOptions ? 'Account Already Exists' : 'Create a new account'}
        </h2>
        {!showExistingEmailOptions && (
          <p className={`mt-2 text-center text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            Or{' '}
            <Link to="/login" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
              sign in to your existing account
            </Link>
          </p>
        )}
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className={`${isDark ? 'bg-dark-200 border border-dark-300 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow'} py-8 px-4 sm:rounded-lg sm:px-10`}>
          {error && (
            <div className={`${isDark ? 'bg-red-900/30 border-red-700' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-6`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className={`h-5 w-5 ${isDark ? 'text-red-500' : 'text-red-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${isDark ? 'text-red-400' : 'text-red-700'}`}>{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className={`${isDark ? 'bg-green-900/30 border-green-700' : 'bg-green-50 border-green-400'} border-l-4 p-4 mb-6`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className={`h-5 w-5 ${isDark ? 'text-green-500' : 'text-green-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${isDark ? 'text-green-400' : 'text-green-700'}`}>{success}</p>
                </div>
              </div>
            </div>
          )}

          {showExistingEmailOptions ? (
            <div className="space-y-6">
              <div className={`${isDark ? 'bg-blue-900/30 border-blue-700' : 'bg-blue-50 border-blue-400'} border-l-4 p-4 mb-6`}>
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className={`h-5 w-5 ${isDark ? 'text-blue-500' : 'text-blue-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm ${isDark ? 'text-blue-400' : 'text-blue-700'}`}>
                      An account with the email <strong>{existingEmail}</strong> already exists. What would you like to do?
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  type="button"
                  onClick={handleSendMagicLink}
                  disabled={magicLinkLoading || resetPasswordLoading}
                  className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                    isDark
                      ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                      : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
                >
                  {magicLinkLoading ? 'Sending...' : 'Send me a magic link to sign in'}
                </button>

                <button
                  type="button"
                  onClick={handleSendPasswordReset}
                  disabled={magicLinkLoading || resetPasswordLoading}
                  className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                    isDark
                      ? 'bg-dark-300 text-neon-magenta border-neon-magenta hover:bg-dark-400 focus:ring-neon-magenta shadow-[0_0_10px_rgba(255,0,255,0.3)]'
                      : 'bg-green-600 text-white border-transparent hover:bg-green-700 focus:ring-green-500'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
                >
                  {resetPasswordLoading ? 'Sending...' : 'Reset my password'}
                </button>

                <button
                  type="button"
                  onClick={handleCancelExistingEmailOptions}
                  disabled={magicLinkLoading || resetPasswordLoading}
                  className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                    isDark
                      ? 'border-dark-400 bg-dark-300 text-gray-300 hover:bg-dark-400'
                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50`}
                >
                  Try a different email
                </button>

                <div className="text-center mt-4">
                  <Link to="/login" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
                    Go to sign in page
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            <form className="space-y-6" onSubmit={handleRegister}>
              <div>
                <label htmlFor="fullName" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Full Name
                </label>
                <div className="mt-1">
                  <input
                    id="fullName"
                    name="fullName"
                    type="text"
                    autoComplete="name"
                    required
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Email address
                </label>
                <div className="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Password
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Confirm Password
                </label>
                <div className="mt-1">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className={`h-4 w-4 ${isDark ? 'text-neon-cyan focus:ring-neon-cyan border-dark-400' : 'text-blue-600 focus:ring-blue-500 border-gray-300'} rounded`}
                />
                <label htmlFor="terms" className={`ml-2 block text-sm ${isDark ? 'text-gray-200' : 'text-gray-900'}`}>
                  I agree to the{' '}
                  <Link to="/terms" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link to="/privacy" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={loading}
                  className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                    isDark
                      ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                      : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
                >
                  {loading ? 'Creating account...' : 'Create account'}
                </button>
              </div>

              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className={`w-full border-t ${isDark ? 'border-dark-400' : 'border-gray-300'}`}></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className={`px-2 ${isDark ? 'bg-dark-200 text-gray-400' : 'bg-white text-gray-500'}`}>Or continue with</span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 gap-3">
                  <button
                    type="button"
                    onClick={handleGoogleSignUp}
                    disabled={googleLoading || loading}
                    className={`w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                      isDark
                        ? 'border-dark-400 bg-dark-300 text-gray-300 hover:bg-dark-400'
                        : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {googleLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-gray-500 rounded-full"></div>
                        <span>Connecting to Google...</span>
                      </div>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z" />
                        </svg>
                        <span>Sign up with Google</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
