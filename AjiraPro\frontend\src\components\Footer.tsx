import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

const Footer: React.FC = () => {
  const { isDark } = useTheme();

  return (
    <footer className={`${isDark ? 'bg-dark-200' : 'bg-gray-800'} text-white py-12 transition-colors duration-300`}>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <motion.h3
              className={`text-xl font-bold mb-4 ${isDark ? 'text-neon-cyan' : 'text-white'}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              AjiraPro
            </motion.h3>
            <motion.p
              className="text-gray-400 mb-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              AI-powered resume and CV builder to help you land your dream job.
            </motion.p>
            <div className="flex space-x-4">
              <motion.a
                href="https://twitter.com/ajirapro"
                target="_blank"
                rel="noopener noreferrer"
                className={`${isDark ? 'text-gray-400 hover:text-neon-magenta' : 'text-gray-400 hover:text-white'} transition-colors duration-200`}
                whileHover={{ scale: 1.2, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
              >
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </motion.a>
              <motion.a
                href="https://linkedin.com/company/ajirapro"
                target="_blank"
                rel="noopener noreferrer"
                className={`${isDark ? 'text-gray-400 hover:text-neon-blue' : 'text-gray-400 hover:text-white'} transition-colors duration-200`}
                whileHover={{ scale: 1.2, rotate: -5 }}
                whileTap={{ scale: 0.9 }}
              >
                <span className="sr-only">LinkedIn</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </motion.a>
            </div>
          </div>

          <div>
            <motion.h3
              className={`text-lg font-semibold mb-4 ${isDark ? 'text-neon-cyan' : 'text-white'}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Products
            </motion.h3>
            <ul className="space-y-2">
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Link
                  to="/resume-builder"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-cyan' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Resume Builder
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Link
                  to="/cv-builder"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-cyan' : 'hover:text-white'} transition-colors duration-200`}
                >
                  CV Builder
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Link
                  to="/resume-templates"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-cyan' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Resume Templates
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                <Link
                  to="/cv-templates"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-cyan' : 'hover:text-white'} transition-colors duration-200`}
                >
                  CV Templates
                </Link>
              </motion.li>
            </ul>
          </div>

          <div>
            <motion.h3
              className={`text-lg font-semibold mb-4 ${isDark ? 'text-neon-magenta' : 'text-white'}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              Resources
            </motion.h3>
            <ul className="space-y-2">
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 }}
              >
                <Link
                  to="/blog"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-magenta' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Blog
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.0 }}
              >
                <Link
                  to="/career-advice"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-magenta' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Career Advice
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.1 }}
              >
                <Link
                  to="/resume-examples"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-magenta' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Resume Examples
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.2 }}
              >
                <Link
                  to="/faq"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-magenta' : 'hover:text-white'} transition-colors duration-200`}
                >
                  FAQ
                </Link>
              </motion.li>
            </ul>
          </div>

          <div>
            <motion.h3
              className={`text-lg font-semibold mb-4 ${isDark ? 'text-neon-blue' : 'text-white'}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.3 }}
            >
              Company
            </motion.h3>
            <ul className="space-y-2">
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.4 }}
              >
                <Link
                  to="/about"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-blue' : 'hover:text-white'} transition-colors duration-200`}
                >
                  About Us
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.5 }}
              >
                <Link
                  to="/contact"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-blue' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Contact
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.6 }}
              >
                <Link
                  to="/privacy"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-blue' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Privacy Policy
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.7 }}
              >
                <Link
                  to="/terms"
                  className={`text-gray-400 ${isDark ? 'hover:text-neon-blue' : 'hover:text-white'} transition-colors duration-200`}
                >
                  Terms of Service
                </Link>
              </motion.li>
            </ul>
          </div>
        </div>

        <motion.div
          className={`border-t ${isDark ? 'border-dark-400' : 'border-gray-700'} mt-8 pt-8 flex flex-col md:flex-row justify-between items-center`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.8 }}
        >
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} AjiraPro. All rights reserved.
          </p>
          <div className="mt-4 md:mt-0 flex space-x-4">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                to="/privacy"
                className={`text-gray-400 ${isDark ? 'hover:text-neon-purple' : 'hover:text-white'} transition-colors duration-200`}
              >
                Privacy
              </Link>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                to="/terms"
                className={`text-gray-400 ${isDark ? 'hover:text-neon-purple' : 'hover:text-white'} transition-colors duration-200`}
              >
                Terms
              </Link>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
