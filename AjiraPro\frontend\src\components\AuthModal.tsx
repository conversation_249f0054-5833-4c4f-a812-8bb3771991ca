import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { Link } from 'react-router-dom';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'signup';
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, initialMode = 'login' }) => {
  const [mode, setMode] = useState<'login' | 'signup'>(initialMode);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [magicLinkMode, setMagicLinkMode] = useState(false);

  const { isDark } = useTheme();
  const { signIn, signUp, isAuthenticated, signInWithMagicLink, signInWithGoogle } = useAuth();

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (isOpen) {
      setError(null);
      setSuccess(null);
      setLoading(false);
      setGoogleLoading(false);
      setMagicLinkMode(false);
    }
  }, [isOpen, mode]);

  // Close modal if user becomes authenticated
  useEffect(() => {
    if (isAuthenticated && isOpen) {
      onClose();
    }
  }, [isAuthenticated, isOpen, onClose]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (magicLinkMode) {
        // Send magic link
        const { error } = await signInWithMagicLink(email);

        if (error) {
          throw error;
        }

        setSuccess('Magic link sent! Please check your email to sign in.');
        setLoading(false);
      } else {
        // Regular password login
        const { error } = await signIn(email, password);

        if (error) {
          throw error;
        }

        // The redirect will be handled by the useEffect above
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred during login');
      setLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      setLoading(false);
      return;
    }

    try {
      const { error } = await signUp(email, password, { full_name: fullName });

      if (error) {
        // If the error message indicates the user already exists, provide a clearer message
        if (error.message && (
            error.message.includes('already registered') ||
            error.message.includes('already exists') ||
            error.message.includes('already taken')
          )) {
          throw new Error('An account with this email already exists. Please sign in or reset your password.');
        }
        throw error;
      }

      setSuccess('Registration successful! Please check your email for verification.');

      // Switch to login mode after successful registration
      setTimeout(() => {
        setMode('login');
        setSuccess(null);
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    try {
      setGoogleLoading(true);
      setError(null);

      const { error } = await signInWithGoogle();

      if (error) {
        throw error;
      }

      // The redirect will be handled by the OAuth provider
      // and the GoogleCallback component
    } catch (error: any) {
      setError(error.message || 'An error occurred during Google sign in');
      setGoogleLoading(false);
    }
  };

  const toggleMagicLinkMode = () => {
    setMagicLinkMode(!magicLinkMode);
    setError(null);
    setSuccess(null);
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: -50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: 'spring', stiffness: 300, damping: 30 }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  const formVariants = {
    hidden: { opacity: 0, x: mode === 'login' ? -20 : 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3 }
    },
    exit: {
      opacity: 0,
      x: mode === 'login' ? 20 : -20,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={overlayVariants}
          onClick={onClose}
        >
          <motion.div
            className={`w-full max-w-md p-6 rounded-lg ${isDark ? 'bg-dark-200 border border-dark-300 shadow-[0_4px_20px_rgba(0,0,0,0.5)]' : 'bg-white shadow-xl'}`}
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {mode === 'login' ? 'Sign In' : 'Create Account'}
              </h2>
              <button
                onClick={onClose}
                className={`p-1 rounded-full ${isDark ? 'text-gray-400 hover:text-white hover:bg-dark-400' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'}`}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Toggle Buttons */}
            <div className={`flex mb-6 p-1 rounded-lg ${isDark ? 'bg-dark-300' : 'bg-gray-100'}`}>
              <button
                className={`flex-1 py-2 text-sm font-medium rounded-md transition-colors ${
                  mode === 'login'
                    ? isDark
                      ? 'bg-dark-400 text-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.2)]'
                      : 'bg-white text-blue-600 shadow'
                    : isDark
                      ? 'text-gray-400 hover:text-gray-200'
                      : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setMode('login')}
              >
                Sign In
              </button>
              <button
                className={`flex-1 py-2 text-sm font-medium rounded-md transition-colors ${
                  mode === 'signup'
                    ? isDark
                      ? 'bg-dark-400 text-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.2)]'
                      : 'bg-white text-blue-600 shadow'
                    : isDark
                      ? 'text-gray-400 hover:text-gray-200'
                      : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setMode('signup')}
              >
                Sign Up
              </button>
            </div>

            {/* Error and Success Messages */}
            {error && (
              <div className={`${isDark ? 'bg-red-900/30 border-red-700' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-6`}>
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className={`h-5 w-5 ${isDark ? 'text-red-500' : 'text-red-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm ${isDark ? 'text-red-400' : 'text-red-700'}`}>{error}</p>
                  </div>
                </div>
              </div>
            )}

            {success && (
              <div className={`${isDark ? 'bg-green-900/30 border-green-700' : 'bg-green-50 border-green-400'} border-l-4 p-4 mb-6`}>
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className={`h-5 w-5 ${isDark ? 'text-green-500' : 'text-green-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm ${isDark ? 'text-green-400' : 'text-green-700'}`}>{success}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Form Container */}
            <AnimatePresence mode="wait">
              <motion.div
                key={mode}
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={formVariants}
              >
                {mode === 'login' ? (
                  /* Login Form */
                  <form className="space-y-4" onSubmit={handleLogin}>
                    <div>
                      <label htmlFor="email" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        Email address
                      </label>
                      <div className="mt-1">
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                        />
                      </div>
                    </div>

                    {!magicLinkMode && (
                      <div>
                        <label htmlFor="password" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                          Password
                        </label>
                        <div className="mt-1">
                          <input
                            id="password"
                            name="password"
                            type="password"
                            autoComplete="current-password"
                            required={!magicLinkMode}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      {!magicLinkMode ? (
                        <div className="text-sm">
                          <Link to="/forgot-password" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`} onClick={onClose}>
                            Forgot your password?
                          </Link>
                        </div>
                      ) : (
                        <div className={`text-sm ${isDark ? 'text-gray-300' : ''}`}>
                          <span>We'll send you a magic link to sign in without a password</span>
                        </div>
                      )}
                    </div>

                    <div>
                      <button
                        type="submit"
                        disabled={loading}
                        className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                          isDark
                            ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                            : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                        } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
                      >
                        {loading ? (magicLinkMode ? 'Sending...' : 'Signing in...') : (magicLinkMode ? 'Send Magic Link' : 'Sign in')}
                      </button>
                    </div>

                    <div className="text-center">
                      <button
                        type="button"
                        onClick={toggleMagicLinkMode}
                        className={`text-sm font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}
                      >
                        {magicLinkMode ? 'Sign in with password instead' : 'Sign in with a magic link instead'}
                      </button>
                    </div>
                  </form>
                ) : (
                  /* Signup Form */
                  <form className="space-y-4" onSubmit={handleSignup}>
                    <div>
                      <label htmlFor="fullName" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        Full Name
                      </label>
                      <div className="mt-1">
                        <input
                          id="fullName"
                          name="fullName"
                          type="text"
                          autoComplete="name"
                          required
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                          className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="email" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        Email address
                      </label>
                      <div className="mt-1">
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="password" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        Password
                      </label>
                      <div className="mt-1">
                        <input
                          id="password"
                          name="password"
                          type="password"
                          autoComplete="new-password"
                          required
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                        Confirm Password
                      </label>
                      <div className="mt-1">
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          autoComplete="new-password"
                          required
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                        />
                      </div>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        required
                        className={`h-4 w-4 ${isDark ? 'text-neon-cyan focus:ring-neon-cyan border-dark-400' : 'text-blue-600 focus:ring-blue-500 border-gray-300'} rounded`}
                      />
                      <label htmlFor="terms" className={`ml-2 block text-sm ${isDark ? 'text-gray-200' : 'text-gray-900'}`}>
                        I agree to the{' '}
                        <Link to="/terms" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`} onClick={onClose}>
                          Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link to="/privacy" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`} onClick={onClose}>
                          Privacy Policy
                        </Link>
                      </label>
                    </div>

                    <div>
                      <button
                        type="submit"
                        disabled={loading}
                        className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                          isDark
                            ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                            : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                        } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
                      >
                        {loading ? 'Creating account...' : 'Create account'}
                      </button>
                    </div>
                  </form>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Social Login */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className={`w-full border-t ${isDark ? 'border-dark-400' : 'border-gray-300'}`}></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className={`px-2 ${isDark ? 'bg-dark-200 text-gray-400' : 'bg-white text-gray-500'}`}>Or continue with</span>
                </div>
              </div>

              <div className="mt-6">
                <button
                  type="button"
                  onClick={handleGoogleAuth}
                  disabled={googleLoading || loading}
                  className={`w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                    isDark
                      ? 'border-dark-400 bg-dark-300 text-gray-300 hover:bg-dark-400'
                      : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {googleLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-gray-500 rounded-full"></div>
                      <span>Connecting to Google...</span>
                    </div>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z" />
                      </svg>
                      <span>Continue with Google</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AuthModal;
