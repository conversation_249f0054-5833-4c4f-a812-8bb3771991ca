# =============================================================================
# Multi-Stage Dockerfile for FajiraPro Backend
# =============================================================================

# -----------------------------------------------------------------------------
# Base Stage - Common dependencies and setup
# -----------------------------------------------------------------------------
FROM python:3.11-slim as base

WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies with caching
RUN --mount=type=cache,id=s/pip-cache,target=/root/.cache/pip \
    pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Download spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY . .

# Make scripts executable
RUN chmod +x /app/web.sh /app/worker.sh /app/start_worker.py

# -----------------------------------------------------------------------------
# FastAPI Stage - Web server
# -----------------------------------------------------------------------------
FROM base as fastapi

# Expose port for FastAPI
EXPOSE 8000

# Set FastAPI-specific environment variables
ENV CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP=False

# Start FastAPI server
CMD ["python", "start.py"]

# -----------------------------------------------------------------------------
# Worker Stage - Celery worker
# -----------------------------------------------------------------------------
FROM base as worker

# Set worker-specific environment variables
ENV CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP=True

# Start Celery worker
CMD ["python", "start_worker.py"]
