import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '../utils/supabase';

const ResetPasswordPage: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we have a valid recovery token in the URL
  useEffect(() => {
    const handleRecoveryToken = async () => {
      try {
        // Check for error parameters that indicate an expired token
        const searchParams = new URLSearchParams(window.location.search);
        const errorCode = searchParams.get('error_code');
        const errorDescription = searchParams.get('error_description');

        if (errorCode === 'otp_expired' || errorCode === 'access_denied' ||
            (errorDescription && (errorDescription.includes('expired') || errorDescription.includes('invalid')))) {
          // Redirect to the expired token page with the error information
          navigate(`/auth/token-expired?error_code=${errorCode || ''}&error_description=${errorDescription || ''}`);
          return;
        }

        // Get the current URL and hash
        const hash = window.location.hash;
        const pathParam = searchParams.get('p');

        // Check if we have a recovery token in the hash or path parameter
        // The hash might look like: #access_token=...&type=recovery&...
        const hasRecoveryToken =
          (hash && (hash.includes('type=recovery') || hash.includes('access_token'))) ||
          (pathParam === '/reset-password');

        if (hasRecoveryToken) {
          // We have a valid recovery token, proceed with password reset

          // Important: Do NOT clean up the URL or remove the hash at this point
          // The hash contains the token needed for password reset

          // If we have a path parameter but no hash, we might need to exchange the token
          if (pathParam === '/reset-password' && !hash) {
            // This is a redirect from Supabase with p=/reset-password
            // We need to check if we have a session
            const { data } = await supabase.auth.getSession();
            if (!data.session) {
              // No session, redirect to forgot password
              navigate('/forgot-password');
              return;
            }
          }
        } else {
          // No valid recovery token found in the hash or path parameter
          // Check if we're in a recovery flow by looking for a session
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            // Error getting session
            navigate('/forgot-password');
            return;
          }

          if (!data.session) {
            // No session and no recovery token, redirect to forgot password
            navigate('/forgot-password');
            return;
          }

          // If we have a session but no recovery token, we might be in a different flow
          // Let's check if the user needs to reset their password
          const user = data.session.user;
          if (user && user.email) {
            // We have a user, but we're not sure if they're in a password reset flow
            // Let's proceed with caution and allow them to reset their password
          } else {
            // No user or email, redirect to forgot password
            navigate('/forgot-password');
          }
        }
      } catch (error) {
        // If there's an error handling the recovery token, redirect to forgot password
        navigate('/forgot-password');
      }
    };

    handleRecoveryToken();
  }, [navigate, location]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      setLoading(false);
      return;
    }

    try {
      // Get the hash from the URL
      const hash = window.location.hash;

      // Parse the hash to extract the access token if it exists
      let accessToken = null;
      if (hash && hash.includes('access_token=')) {
        const hashParams = new URLSearchParams(hash.substring(1)); // Remove the # character
        accessToken = hashParams.get('access_token');
      }

      // Check if we have a recovery token in the hash
      const hasRecoveryToken = hash && (hash.includes('type=recovery') || hash.includes('access_token'));

      // First, check if we have a session
      const { data: sessionData } = await supabase.auth.getSession();

      // If we don't have a session and no recovery token, we can't reset the password
      if (!sessionData.session && !hasRecoveryToken) {
        setError('No valid recovery token found. Please request a new password reset link.');
        setLoading(false);
        return;
      }

      // If we have an access token from the hash, set it in the auth
      if (accessToken) {
        // Set the access token in the auth
        const { error: setSessionError } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: '', // We don't have a refresh token in this flow
        });

        if (setSessionError) {
          // If we can't set the session, try to continue anyway
          // The updateUser call might still work with the token in the URL
        }
      }

      // Update the user's password
      // If we have a recovery token in the URL hash, Supabase will automatically use it
      const { data, error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        // If we get an error about missing session, try a different approach
        if (error.message.includes('Auth session missing') && accessToken) {
          // Try to use the access token directly
          // Use the hardcoded Supabase URL
          const supabaseUrl = 'https://xmmbjopzpyotkyyfqzyq.supabase.co';
          const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
              password: password
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to update password');
          }

          // If we get here, the password was updated successfully
        } else {
          throw error;
        }
      }

      if (data && !data.user) {
        throw new Error('Failed to update password. Please try again.');
      }

      setSuccess('Your password has been reset successfully!');

      // Redirect to login after a delay
      setTimeout(() => {
        // Sign out to ensure a clean state
        supabase.auth.signOut().then(() => {
          // Clear any hash or query parameters
          window.history.replaceState({}, document.title, '/login');
          navigate('/login');
        });
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'An error occurred while resetting your password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Set new password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Enter your new password below
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">{success}</p>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleResetPassword}>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                New Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm New Password
              </label>
              <div className="mt-1">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Resetting...' : 'Reset Password'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="flex items-center justify-center">
              <div className="text-sm">
                <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500">
                  Back to sign in
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
