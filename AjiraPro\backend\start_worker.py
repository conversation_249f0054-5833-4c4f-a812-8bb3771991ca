#!/usr/bin/env python3
"""
Dedicated Celery Worker Startup Script for Railway
This script ONLY starts the Celery worker - no FastAPI
"""

import os
import sys
import subprocess

# Set environment variables for Railway
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "False")

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point - ONLY starts Celery worker"""
    print("🎯 FajiraPro Celery Worker")
    print("=" * 50)

    # Check critical environment variables
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        print("❌ REDIS_URL not found!")
        sys.exit(1)

    print(f"✅ Redis URL: {redis_url[:20]}...")
    print(f"✅ Environment: {os.getenv('ENVIRONMENT', 'unknown')}")
    print()

    print("🔧 Starting Celery Worker...")
    print("=" * 40)

    try:
        # Start celery worker with Railway-optimized settings
        cmd = [
            "celery",
            "-A", "app.celery_worker.celery",
            "worker",
            "--loglevel=info",
            "--concurrency=1",  # Single worker for Railway
            "--pool=solo"       # Solo pool for Railway compatibility
        ]

        print(f"Executing: {' '.join(cmd)}")
        print("Worker starting...")
        print()

        # Run the worker (this blocks until worker stops)
        subprocess.run(cmd, check=True)

    except subprocess.CalledProcessError as e:
        print(f"❌ Celery worker failed with exit code {e.returncode}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("Worker interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting Celery worker: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
