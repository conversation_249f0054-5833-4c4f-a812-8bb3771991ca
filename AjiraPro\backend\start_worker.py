#!/usr/bin/env python3
"""
Dedicated Celery Worker Startup Script
This script ONLY starts the Celery worker - no FastAPI
"""

import os
import sys
import subprocess
import signal

# Set environment variables for Railway
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "False")

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_environment():
    """Check critical environment variables for worker"""
    print("🔧 Worker Environment Check")
    print("=" * 40)
    
    required_vars = {
        "SUPABASE_URL": "Database connection",
        "SUPABASE_KEY": "Database authentication", 
        "REDIS_URL": "Task queue",
        "CLAUDE_API_KEY": "Resume parsing",
        "OPENAI_API_KEY": "Content scoring",
        "GEMINI_API_KEY": "Resume validation"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set ({description})")
        else:
            print(f"❌ {var}: Missing ({description})")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing critical environment variables!")
        print("The Celery worker may fail without these.")
        return False
    
    print("✅ All environment variables are set!")
    return True

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    del frame  # Unused parameter
    print(f"Received signal {signum}, shutting down worker...")
    sys.exit(0)

def start_celery_worker():
    """Start Celery worker process"""
    print("🔧 Starting Celery Worker...")
    print("=" * 40)
    
    try:
        # Set up signal handlers
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        # Start celery worker with proper configuration
        cmd = [
            "celery", 
            "-A", "app.celery_worker.celery", 
            "worker", 
            "--loglevel=info",
            "--concurrency=2",
            "--pool=solo"  # Use solo pool for Railway compatibility
        ]
        
        print(f"Executing: {' '.join(cmd)}")
        print("Worker starting...")
        
        # Run the worker
        subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Celery worker failed: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("Worker interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting Celery worker: {e}")
        sys.exit(1)

def main():
    """Main entry point - ONLY starts worker"""
    print("🎯 FajiraPro Celery Worker")
    print("=" * 50)
    print(f"Timestamp: {os.getenv('RAILWAY_DEPLOYMENT_ID', 'local')}")
    print()
    
    # Check environment
    env_ok = check_environment()
    if not env_ok:
        print("\n⚠️  Proceeding anyway, but worker may fail...")
    
    print()
    
    # Start worker (this is the ONLY thing this script does)
    start_celery_worker()

if __name__ == "__main__":
    main()
