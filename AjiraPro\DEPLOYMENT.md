# AjiraPro Deployment Guide

This document provides detailed instructions for deploying the AjiraPro application to Railway.

## Project Overview

AjiraPro consists of two main components:

1. **Frontend**: React application
2. **Backend**: FastAPI application with Celery for background tasks

## Railway Deployment

The backend is deployed on Railway as part of the AjiraProMax project.

### Prerequisites

1. Railway account with access to the AjiraProMax project
2. GitHub account with access to the ProDevDenis/fAjiraPro repository
3. Supabase project with the necessary tables and storage buckets
4. OpenAI API key for AI features

### Backend Deployment Steps

1. **Set Up Railway Project**

   - Create a new project in Railway named "AjiraProMax"
   - Connect it to the GitHub repository (ProDevDenis/fAjiraPro)

2. **Create Services**

   a. **FastAPI Service**
      - Create a new service from GitHub
      - Select the repository and branch
      - Set the root directory to `AjiraPro/backend/`
      - Set the start command to `/app/web.sh`

   b. **Redis Service**
      - Add a new Redis service from the Railway plugins
      - No additional configuration needed

   c. **Celery Worker Service**
      - Create another service from the same GitHub repository
      - Set the root directory to `AjiraPro/backend/`
      - Set the start command to `/app/worker.sh`

3. **Configure Environment Variables**

   Set the following environment variables for both the FastAPI and Celery Worker services:

   - `SUPABASE_URL`: Your Supabase project URL
   - `SUPABASE_KEY`: Your Supabase anon/public key (not the service role key)
   - `REDIS_URL`: Will be automatically set by Railway when using the Redis plugin
   - `ENVIRONMENT`: Set to `production`
   - `DEBUG`: Set to `False`
   - `OPENAI_API_KEY`: Your OpenAI API key

4. **Deploy Services**

   - Railway will automatically deploy the services when changes are pushed to the repository
   - You can also manually trigger deployments from the Railway dashboard

5. **Verify Deployment**

   - Check the logs for each service to ensure they started correctly
   - Access the root endpoint (/) to verify the API is running
   - Check the health endpoint (/health) for detailed service status

### Frontend Deployment

The frontend can be deployed to Cloudflare Pages or Vercel:

1. **Cloudflare Pages**
   - Connect your GitHub repository
   - Set the build command to `cd AjiraPro/frontend && npm install && npm run build`
   - Set the build output directory to `AjiraPro/frontend/build`
   - Configure environment variables for the API URL

2. **Vercel**
   - Connect your GitHub repository
   - Set the root directory to `AjiraPro/frontend`
   - Vercel will automatically detect the React application
   - Configure environment variables for the API URL

## Troubleshooting

### Common Deployment Issues

1. **Docker Build Failures**
   - Check the Dockerfile for errors
   - Ensure all dependencies are correctly specified
   - Verify that the start commands are correct

2. **Environment Variable Issues**
   - Verify all required environment variables are set
   - Check for typos in variable names (they are case-sensitive)
   - Ensure there are no extra spaces or characters in the values

3. **Service Communication Issues**
   - Verify that Redis is running and accessible
   - Check that the REDIS_URL is correctly set in both services
   - Ensure the Celery worker can connect to Redis

4. **Database Connection Issues**
   - Verify the Supabase URL and key are correct
   - Check that the necessary tables exist in the database
   - Ensure the Row Level Security (RLS) policies are correctly configured

### Debugging

The application includes several debugging endpoints:

- `/` - Shows basic application information and environment variable status
- `/health` - Provides detailed health check information for all services

## Updating the Deployment

To update the deployment:

1. Make changes to the codebase
2. Commit and push to the GitHub repository
3. Railway will automatically detect the changes and start the deployment process
4. Monitor the deployment logs for any issues

## Rollback Procedure

If a deployment fails or introduces issues:

1. Go to the Railway dashboard
2. Select the service with issues
3. Go to the "Deployments" tab
4. Find the last working deployment
5. Click on the three dots menu and select "Rollback to this deployment"

## Monitoring

Railway provides basic monitoring capabilities:

1. **Logs**: View real-time logs for each service
2. **Metrics**: Monitor CPU and memory usage
3. **Deployments**: Track deployment history and status

For more advanced monitoring, consider integrating with a third-party service like Datadog or New Relic.
