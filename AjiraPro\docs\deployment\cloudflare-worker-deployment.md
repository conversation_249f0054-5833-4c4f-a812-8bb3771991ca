# Cloudflare Worker Deployment Guide for PDF Proxy

This guide provides step-by-step instructions for deploying the PDF Proxy Cloudflare Worker for FajiraPro.

## Overview

The PDF Proxy Worker is responsible for securely serving PDF files from Supabase Storage to users in the browser. It:

1. Receives requests to `/pdf/:resumeId`
2. Fetches the resume data from Supabase to get the file path
3. Generates a signed URL for the file
4. Proxies the PDF content to the client with appropriate security headers

## Prerequisites

- Cloudflare account with Workers enabled
- Wrangler CLI installed (`npm install -g wrangler`)
- Supabase project with service role key
- Access to the FajiraPro GitHub repository

## Deployment Steps

### 1. Install Wrangler CLI

```bash
npm install -g wrangler
```

### 2. Login to Cloudflare

```bash
wrangler login
```

This will open a browser window to authenticate with your Cloudflare account.

### 3. Navigate to the Functions Directory

```bash
cd AjiraPro/frontend/functions
```

### 4. Set the Supabase Service Role Key

```bash
wrangler secret put SUPABASE_SERVICE_ROLE_KEY
```

When prompted, enter your Supabase service role key. You can find this in the Supabase dashboard under Project Settings > API > Project API keys > service_role key.

### 5. Deploy the Worker

```bash
wrangler deploy
```

This will deploy the worker to Cloudflare and provide you with a URL to access it.

### 6. Configure Custom Domain

By default, the worker will be available at a workers.dev subdomain. To use it with your custom domain:

1. Go to the Cloudflare dashboard
2. Select your domain (ajirapro.com)
3. Go to Workers & Pages > Add route
4. Add a route pattern: `ajirapro.com/pdf/*`
5. Select the worker: `ajirapro-pdf-proxy`
6. Click Save

## Troubleshooting

### Common Issues

#### 1. 404 Error When Accessing PDFs

If you're getting a 404 error when trying to access PDFs, check:

- The worker is deployed correctly
- The route is configured correctly in Cloudflare
- The Supabase service role key is set correctly
- The resume exists in the Supabase database
- The file exists in the Supabase storage bucket

#### 2. CORS Errors

If you're seeing CORS errors in the browser console:

- Check the CORS headers in the worker code
- Ensure the `Access-Control-Allow-Origin` header is set correctly

#### 3. Authentication Errors

If you're seeing authentication errors when the worker tries to access Supabase:

- Verify the Supabase service role key is set correctly
- Check that the key has not expired or been revoked

## Monitoring and Logs

To monitor the worker and view logs:

1. Go to the Cloudflare dashboard
2. Select Workers & Pages
3. Click on your worker (ajirapro-pdf-proxy)
4. Go to the Logs tab

You can also use the Wrangler CLI to view logs:

```bash
wrangler tail
```

## Updating the Worker

To update the worker after making changes:

1. Make your changes to the worker code
2. Run `wrangler deploy` again

## Security Considerations

The worker implements several security measures:

1. **Content Security Policy (CSP)**: Restricts which resources can be loaded
2. **X-Content-Type-Options**: Prevents MIME type sniffing
3. **X-Frame-Options**: Controls how the content can be embedded in iframes
4. **Referrer Policy**: Controls how much referrer information is included
5. **Permissions Policy**: Restricts which browser features can be used

## Testing the Worker

To test the worker locally before deployment:

```bash
wrangler dev
```

This will start a local development server that you can use to test the worker.

## Rollback Procedure

If you need to rollback to a previous version:

1. Go to the Cloudflare dashboard
2. Select Workers & Pages
3. Click on your worker (ajirapro-pdf-proxy)
4. Go to the Deployments tab
5. Find the previous version you want to rollback to
6. Click on the three dots menu and select "Rollback to this version"
