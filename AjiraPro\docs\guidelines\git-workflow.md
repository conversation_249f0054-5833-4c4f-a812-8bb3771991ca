Git Workflow Guidelines
Branching Strategy (GitFlow)
Main Branches
•	main - Production-ready code
•	develop - Integration branch for feature development
Supporting Branches
•	feature/* - New features and non-emergency bugfixes
•	hotfix/* - Urgent production fixes
•	release/* - Release preparation
Branch Naming Convention
•	feature/resume-tailoring
•	feature/payment-integration
•	hotfix/auth-token-expiry
•	release/v1.0.0
Commit Message Format
Use semantic commit messages:
<type>(<scope>): <subject>

<body>

<footer>
Types
•	feat: New feature
•	fix: Bug fix
•	docs: Documentation changes
•	style: Code style changes (formatting, etc.)
•	refactor: Code changes that neither fix bugs nor add features
•	test: Adding or updating tests
•	chore: Changes to build process, dependencies, etc.
Examples
feat(resume): add ATS compliance scoring algorithm

Implement resume scoring based on keyword matching and formatting rules.
Score is calculated on a 100-point scale with detailed feedback.

Closes #123
fix(auth): resolve token expiration issue

Fix bug where auth tokens weren't being refreshed properly,
causing users to be logged out unexpectedly.

Fixes #456
Pull Request Process
1.	Create PR from feature branch to develop
o	Include description of changes
o	Reference relevant issues
o	Assign reviewers
2.	Code Review
o	At least one approval required
o	Address all review comments
o	All tests must pass
3.	Merge Strategy
o	Squash and merge for cleaner history
o	Include PR number in squash commit
4.	Post-Merge
o	Delete feature branch after merge
o	Update related issues
Release Process
1.	Create Release Branch
o	Branch from develop
o	Name format: release/v1.0.0
2.	Stabilization
o	Only bugfixes, no new features
o	Update version numbers
o	Prepare release notes
3.	Merge to Main
o	Create PR from release branch to main
o	Requires approval from tech lead
o	Tag with version number after merge
4.	Merge Back to Develop
o	Merge release branch back to develop
Hotfix Process
1.	Create Hotfix Branch
o	Branch from main
o	Name format: hotfix/issue-description
2.	Fix and Test
o	Implement fix
o	Add tests
Merge to Main and Develop
Create PR to main
After merge, create PR to develop
Tag with updated version

