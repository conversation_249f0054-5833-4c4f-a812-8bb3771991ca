import os
from celery import Celery
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Redis URL from environment variable or use default
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# Create Celery instance
celery = Celery(
    "ajirapro",
    broker=redis_url,
    backend=redis_url,
    include=["app.tasks", "app.tasks.resume_analysis"]
)

# Configure Celery
celery.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_concurrency=2,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_time_limit=600,  # 10 minutes
    broker_connection_retry_on_startup=True,
    worker_prefetch_multiplier=1,
    task_track_started=True,
)

# Optional: Configure task routes
celery.conf.task_routes = {
    "app.tasks.generate_resume_task": {"queue": "resume_generation"},
    "app.tasks.legacy_analyze_resume_task": {"queue": "resume_analysis"},
    "app.tasks.resume_analysis.analyze_resume_task": {"queue": "resume_analysis"},
    "app.tasks.resume_analysis.parse_resume_task": {"queue": "resume_analysis"},
    "app.tasks.resume_analysis.score_format_task": {"queue": "resume_analysis"},
    "app.tasks.resume_analysis.get_analysis_status": {"queue": "resume_analysis"},
}

if __name__ == "__main__":
    celery.start()
