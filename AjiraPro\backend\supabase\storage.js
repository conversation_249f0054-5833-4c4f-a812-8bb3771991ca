/**
 * This script can be run to create the necessary storage buckets in Supabase.
 * You'll need to run this with the Supabase CLI or manually create these buckets
 * through the Supabase dashboard.
 */

const createBuckets = async (supabase) => {
  // Create resumes bucket
  const { data: resumesBucket, error: resumesError } = await supabase.storage.createBucket('resumes', {
    public: false,
    fileSizeLimit: 5242880, // 5MB
    allowedMimeTypes: ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  });
  
  if (resumesError) {
    console.error('Error creating resumes bucket:', resumesError);
  } else {
    console.log('Resumes bucket created successfully');
  }
  
  // Create cvs bucket
  const { data: cvsBucket, error: cvsError } = await supabase.storage.createBucket('cvs', {
    public: false,
    fileSizeLimit: 5242880, // 5MB
    allowedMimeTypes: ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  });
  
  if (cvsError) {
    console.error('Error creating cvs bucket:', cvsError);
  } else {
    console.log('CVs bucket created successfully');
  }
  
  // Create templates bucket
  const { data: templatesBucket, error: templatesError } = await supabase.storage.createBucket('templates', {
    public: true, // Templates are public for easy access
    fileSizeLimit: 5242880, // 5MB
    allowedMimeTypes: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  });
  
  if (templatesError) {
    console.error('Error creating templates bucket:', templatesError);
  } else {
    console.log('Templates bucket created successfully');
  }
  
  // Create avatars bucket for user profile pictures
  const { data: avatarsBucket, error: avatarsError } = await supabase.storage.createBucket('avatars', {
    public: true, // Avatars are public for easy access
    fileSizeLimit: 2097152, // 2MB
    allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif']
  });
  
  if (avatarsError) {
    console.error('Error creating avatars bucket:', avatarsError);
  } else {
    console.log('Avatars bucket created successfully');
  }
};

// Note: This script is meant to be run with the Supabase client
// You can run it with the Supabase CLI or manually create these buckets
// through the Supabase dashboard
