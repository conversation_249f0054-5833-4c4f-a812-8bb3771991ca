import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ViewIcon, DownloadIcon, DeleteIcon, ActionIcon, TailorIcon, CreateIcon } from '../ui/icons';
import { useTheme } from '../../contexts/ThemeContext';

interface DocumentActionsProps {
  id: string;
  type: 'resume';
  status?: string;
  onDelete: (id: string) => void;
  onView: (id: string) => void;
  onDownload: (id: string) => void;
}

const DocumentActions: React.FC<DocumentActionsProps> = ({
  id,
  type,
  status = 'completed',
  onDelete,
  onView,
  onDownload
}) => {
  const { isDark } = useTheme();
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const iconClass = `w-5 h-5 ${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`;
  const buttonClass = `p-1.5 rounded-full transition-colors duration-200 ${
    isDark ? 'hover:bg-dark-400' : 'hover:bg-gray-100'
  }`;



  return (
    <div className="flex items-center space-x-1">
      <button
        className={buttonClass}
        title="View"
        onClick={() => onView(id)}
      >
        <ViewIcon className={iconClass} />
      </button>

      <button
        className={buttonClass}
        title="Download"
        onClick={() => onDownload(id)}
      >
        <DownloadIcon className={iconClass} />
      </button>

      <button
        className={buttonClass}
        title="Delete"
        onClick={() => onDelete(id)}
      >
        <DeleteIcon className={iconClass} />
      </button>

      <div className="relative" ref={dropdownRef}>
        <button
          className={buttonClass}
          title="More Actions"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          <ActionIcon className={iconClass} />
        </button>

        {showDropdown && (
          <div
            className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg z-10 ${
              isDark
                ? 'bg-dark-300 border border-dark-400'
                : 'bg-white border border-gray-200'
            }`}
          >
            <div className="py-1">
              {/* Status-based optimization action */}
              {status === 'pending_analysis' ? (
                <Link
                  to={`/resume-results?id=${id}`}
                  className={`flex items-center px-4 py-2 text-sm ${
                    isDark
                      ? 'text-gray-300 hover:bg-dark-400'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setShowDropdown(false)}
                >
                  <CreateIcon className="w-4 h-4 mr-2" />
                  Finish Optimization
                </Link>
              ) : (
                <Link
                  to={`/resume-optimizer?resumeId=${id}`}
                  className={`flex items-center px-4 py-2 text-sm ${
                    isDark
                      ? 'text-gray-300 hover:bg-dark-400'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setShowDropdown(false)}
                >
                  <CreateIcon className="w-4 h-4 mr-2" />
                  Re-Optimize
                </Link>
              )}

              <Link
                to={`/tailor-${type}/${id}`}
                className={`flex items-center px-4 py-2 text-sm ${
                  isDark
                    ? 'text-gray-300 hover:bg-dark-400'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setShowDropdown(false)}
              >
                <TailorIcon className="w-4 h-4 mr-2" />
                Tailor to a Job
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentActions;
