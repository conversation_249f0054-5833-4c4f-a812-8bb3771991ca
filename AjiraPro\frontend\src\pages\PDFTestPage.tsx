import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import EnhancedPDFViewer from '../components/EnhancedPDFViewer';

const PDFTestPage: React.FC = () => {
  const { isDark } = useTheme();
  const { isAuthenticated, user } = useAuth();
  const [filePath, setFilePath] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // Example file paths for testing (replace with actual file paths from your database)
  const examplePaths: string[] = [
    // Format: user_id/file_uuid.pdf
    // You'll need to replace these with actual file paths from your Supabase storage
  ];

  const handleFilePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilePath(e.target.value);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  if (!isAuthenticated) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${isDark ? 'bg-dark-400' : 'bg-gray-50'}`}>
        <div className={`text-center p-8 rounded-lg ${isDark ? 'bg-dark-200 text-white' : 'bg-white text-gray-800'} shadow-lg`}>
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p>Please log in to test the PDF preview functionality.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-400' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 py-8">
        <h1 className={`text-3xl font-bold mb-6 ${isDark ? 'text-white' : 'text-gray-800'}`}>
          PDF Preview Test Page
        </h1>

        {/* File Path Input */}
        <div className={`mb-6 p-6 rounded-lg shadow-lg ${isDark ? 'bg-dark-200' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Test PDF Preview
          </h2>

          <div className="mb-4">
            <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              File Path (format: user_id/file_uuid.pdf)
            </label>
            <input
              type="text"
              value={filePath}
              onChange={handleFilePathChange}
              placeholder={`${user?.id}/example-file-uuid.pdf`}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isDark
                  ? 'bg-dark-300 border-dark-100 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
          </div>

          {examplePaths.length > 0 && (
            <div className="mb-4">
              <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                Example Paths:
              </label>
              <div className="space-y-2">
                {examplePaths.map((path, index) => (
                  <button
                    key={index}
                    onClick={() => setFilePath(path)}
                    className={`block w-full text-left px-3 py-2 rounded border transition-colors ${
                      isDark
                        ? 'bg-dark-300 border-dark-100 text-gray-300 hover:bg-dark-100'
                        : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {path}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            <p className="mb-2">
              <strong>Current User ID:</strong> {user?.id}
            </p>
            <p className="mb-2">
              <strong>Note:</strong> You can only preview files that belong to your user ID.
            </p>
            <p>
              <strong>Backend Endpoint:</strong> /resumes/pdf-preview/{'{file_path}'}
            </p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className={`mb-6 p-4 rounded-lg ${isDark ? 'bg-red-900 text-red-200' : 'bg-red-50 text-red-800'} border ${isDark ? 'border-red-700' : 'border-red-200'}`}>
            <h3 className="font-semibold mb-2">Error:</h3>
            <p>{error}</p>
          </div>
        )}

        {/* PDF Preview */}
        {filePath && (
          <div className={`rounded-lg shadow-lg overflow-hidden ${isDark ? 'bg-dark-200' : 'bg-white'}`}>
            <div className={`p-4 border-b ${isDark ? 'border-dark-100' : 'border-gray-200'}`}>
              <h2 className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-gray-800'}`}>
                PDF Preview: {filePath}
              </h2>
            </div>
            <div style={{ height: '600px' }}>
              <EnhancedPDFViewer
                filePath={filePath}
                fileName={`test-${filePath.split('/').pop()}`}
                className="h-full"
                onError={handleError}
              />
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className={`mt-6 p-6 rounded-lg ${isDark ? 'bg-dark-200' : 'bg-white'} shadow-lg`}>
          <h2 className={`text-xl font-semibold mb-4 ${isDark ? 'text-white' : 'text-gray-800'}`}>
            Testing Instructions
          </h2>
          <div className={`space-y-3 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            <div>
              <h3 className="font-semibold">1. Backend Setup:</h3>
              <p>Make sure your FastAPI backend is running with the new PDF preview endpoint.</p>
            </div>
            <div>
              <h3 className="font-semibold">2. File Path Format:</h3>
              <p>Use the format: user_id/file_uuid.pdf (e.g., {user?.id}/12345678-1234-1234-1234-123456789abc.pdf)</p>
            </div>
            <div>
              <h3 className="font-semibold">3. Authentication:</h3>
              <p>The component automatically uses your current authentication token.</p>
            </div>
            <div>
              <h3 className="font-semibold">4. Error Handling:</h3>
              <p>Any errors will be displayed above the PDF viewer with detailed information.</p>
            </div>
            <div>
              <h3 className="font-semibold">5. Features to Test:</h3>
              <ul className="list-disc pl-5 space-y-1">
                <li>PDF loading and rendering</li>
                <li>Page navigation (if multi-page PDF)</li>
                <li>Zoom controls</li>
                <li>Download functionality</li>
                <li>Automatic URL refresh (every 50 seconds)</li>
                <li>Error handling for invalid paths</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFTestPage;
