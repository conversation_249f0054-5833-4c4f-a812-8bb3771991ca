#!/bin/bash

# Navigate to the frontend directory
cd AjiraPro/frontend

# Install dependencies using npm install instead of npm ci
npm install

# Build the project
npm run build

# Copy the _redirects and _headers files to the build directory
# (This is a fallback in case they're not already included)
cp public/_redirects build/ 2>/dev/null || :
cp public/_headers build/ 2>/dev/null || :
cp public/404.html build/ 2>/dev/null || :

echo "Build completed successfully!"
