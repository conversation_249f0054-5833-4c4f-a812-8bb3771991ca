#!/usr/bin/env python3
"""
Test script to verify Celery worker is functioning
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_celery_connection():
    """Test basic Celery connection"""
    try:
        from app.celery_worker import celery
        
        print("🔧 Testing Celery Connection")
        print("=" * 40)
        
        # Test Redis connection
        redis_url = os.getenv("REDIS_URL", "Not set")
        print(f"Redis URL: {redis_url}")
        
        # Ping workers
        print("Pinging Celery workers...")
        ping_result = celery.control.ping(timeout=5.0)
        
        if ping_result:
            print(f"✅ Workers responded: {ping_result}")
            return True
        else:
            print("❌ No workers responded")
            return False
            
    except Exception as e:
        print(f"❌ Celery connection failed: {str(e)}")
        return False

def test_task_submission():
    """Test submitting a simple task"""
    try:
        from app.tasks import generate_resume_task
        
        print("\n🚀 Testing Task Submission")
        print("=" * 40)
        
        # Submit a test task
        test_data = {
            "resume_id": "test-123",
            "test": True,
            "timestamp": datetime.now().isoformat()
        }
        
        print("Submitting test task...")
        task_result = generate_resume_task.delay(test_data)
        
        print(f"✅ Task submitted with ID: {task_result.id}")
        
        # Wait for result (with timeout)
        print("Waiting for task completion (10 seconds timeout)...")
        try:
            result = task_result.get(timeout=10)
            print(f"✅ Task completed successfully: {result}")
            return True
        except Exception as e:
            print(f"⚠️  Task timeout or error: {str(e)}")
            print("This might be normal if worker is not running")
            return False
            
    except Exception as e:
        print(f"❌ Task submission failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 Celery Worker Test Suite")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Test 1: Connection
    connection_ok = test_celery_connection()
    
    # Test 2: Task submission (only if connection works)
    if connection_ok:
        task_ok = test_task_submission()
    else:
        print("\n⏭️  Skipping task test due to connection failure")
        task_ok = False
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 40)
    print(f"Connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"Task Execution: {'✅ PASS' if task_ok else '❌ FAIL'}")
    
    if connection_ok and task_ok:
        print("\n🎉 All tests passed! Celery worker is functioning correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the logs above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
