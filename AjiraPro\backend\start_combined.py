#!/usr/bin/env python3
"""
Combined startup script that runs both FastAPI and Celery worker
This is a temporary solution - ideally they should be separate services
"""

import os
import sys
import subprocess
import signal
import time
from threading import Thread

# Set environment variables for Railway
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "False")

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_environment():
    """Check critical environment variables"""
    print("🔧 Environment Check")
    print("=" * 40)

    required_vars = {
        "SUPABASE_URL": "Database connection",
        "SUPABASE_KEY": "Database authentication",
        "REDIS_URL": "Task queue",
        "CLAUDE_API_KEY": "Resume parsing",
        "OPENAI_API_KEY": "Content scoring",
        "GEMINI_API_KEY": "Resume validation"
    }

    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set ({description})")
        else:
            print(f"❌ {var}: Missing ({description})")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n⚠️  Missing critical environment variables!")
        print("The Celery worker may fail without these.")
        return False

    print("✅ All environment variables are set!")
    return True

def run_web_server():
    """Run the FastAPI web server"""
    port = int(os.environ.get("PORT", 8000))
    
    print(f"Starting FastAPI server on port {port}")
    print(f"Environment: {os.environ.get('ENVIRONMENT', 'Not set')}")
    print(f"CORS_ORIGINS: {os.environ.get('CORS_ORIGINS', 'Not set')}")
    
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True
    )

def run_celery_worker():
    """Run the Celery worker"""
    print("Starting Celery worker...")

    # Wait a bit for the web server to start
    time.sleep(5)

    try:
        # Run celery worker
        subprocess.run([
            "celery", "-A", "app.celery_worker.celery",
            "worker", "--loglevel=info", "--concurrency=2"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Celery worker failed: {e}")
    except Exception as e:
        print(f"Error starting Celery worker: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    del frame  # Unused parameter
    print(f"Received signal {signum}, shutting down...")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    print("Starting combined FastAPI + Celery worker...")

    # Check environment first
    env_ok = check_environment()
    if not env_ok:
        print("\n⚠️  Proceeding anyway, but some features may not work...")

    # Start Celery worker in a separate thread
    worker_thread = Thread(target=run_celery_worker, daemon=True)
    worker_thread.start()

    # Run web server in main thread
    run_web_server()
