System Architecture
System Components
Component	Technology	Purpose
Frontend	React (TypeScript), Tailwind CSS, Framer Motion	Responsive UI with animations
Backend	FastAPI (Python), Celery, Redis	AI workflows, async tasks, payment processing
Database	Supabase PostgreSQL (Row-Level Security)	User data, resumes, payment records
Auth	Supabase Auth (Email/Google)	Secure user authentication
Storage	Supabase Storage (signed URLs)	Resume/CV file storage (PDF/Word)
AI/ML	OpenAI GPT, spaCy, LangChain	Content generation, ATS checks, NLP
Payments	Flutterwave (Inline + Webhooks)	Payment processing and validation
Hosting	Cloudflare Pages (Frontend), Railway (Backend)	Global CDN, scalable backend
Admin	Custom dashboard (React + FastAPI)	View job requests, logs and usage stats
Dependencies 
Backend (Python)
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0
supabase==2.6.0
python-docx==0.8.11
docxtpl==0.16.2
openai==1.3.6
langchain==0.0.346
spacy==3.7.2
pypdf2==3.0.1
httpx==0.26.0
celery==5.3.4
redis==4.6.0
python-multipart==0.0.6 # For file uploads

# Optional: NLP models for ATS checks
en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.0/en_core_web_sm-3.7.0.tar.gz

# Dev Tools (Testing/Linting)
pytest==7.4.3
black==23.11.0
# All other required dependencies on the go (as development progresses)
Data Flow
1.	User Input → Frontend → Backend API → AI Processing → Database/Storage → User Download
2.	Payment Flow: Flutterwave → Webhook → Backend → Unlock Downloads
Folder Structure 
ResumeAI/  
├── frontend/                   # React (TypeScript)  
│   ├── public/                # Static assets (images, favicon)  
│   ├── src/  
│   │   ├── components/        # Reusable UI components  
│   │   │   ├── auth/          # Login/Signup forms  
│   │   │   ├── payment/       # Flutterwave integration  
│   │   │   └── shared/        # Buttons, modals, loaders  
│   │   ├── hooks/             # Custom hooks  
│   │   │   ├── useSupabase.ts # Supabase auth/data hooks  
│   │   │   └── usePayment.ts  # Flutterwave payment logic  
│   │   ├── pages/             # Next.js-style routing  
│   │   │   ├── Dashboard.tsx  # User's saved resumes  
│   │   │   ├── Builder.tsx    # Step-by-step form  
│   │   │   └── Revamp.tsx     # CV revamp upload flow  
│   │   ├── types/             # TypeScript interfaces  
│   │   │   ├── Resume.ts      # Resume data structure  
│   │   │   └── User.ts        # User profile data  
│   │   ├── utils/             # Helper functions  
│   │   │   ├── api.ts         # Axios instance for backend  
│   │   │   └── validation.ts  # Form validation rules  
│   │   ├── App.tsx            # Root component  
│   │   └── main.tsx           # Entry point  
│   ├── .env                   # Frontend env vars (e.g., Supabase URL)  
│   ├── tsconfig.json          # TypeScript config  
│   └── package.json  
│  
├── backend/                   # FastAPI (Python)  
│   ├── app/  
│   │   ├── routers/           # API endpoints  
│   │   │   ├── auth.py        # User registration/login  
│   │   │   ├── resume.py      # Resume generation/ATS checks  
│   │   │   └── payment.py     # Flutterwave webhooks  
│   │   ├── services/          # Business logic  
│   │   │   ├── ai.py          # OpenAI/LangChain integration  
│   │   │   ├── supabase.py    # DB/storage operations  
│   │   │   └── celery_tasks.py# Async tasks (PDF generation)  
│   │   ├── models/            # Pydantic schemas  
│   │   │   ├── resume.py      # Resume request/response models  
│   │   │   └── user.py        # User auth models  
│   │   ├── core/              # Config and middleware  
│   │   │   ├── config.py      # Env vars and settings  
│   │   │   └── security.py    # JWT authentication  
│   │   ├── templates/         # Word templates  
│   │   │   └── modern.docx    # Prebuilt ATS-friendly template  
│   │   └── main.py            # FastAPI app initialization  
│   ├── tests/                 # Integration/unit tests  
│   ├── requirements.txt       # Python dependencies  
│   └── Dockerfile             # Optional for containerization  
│  
└── supabase/                  # SQL migrations (optional)  
    ├── migrations/            # Database schema  
    └── config.yml             # RLS policies
Security Architecture 
•	All requests authenticated via JWT from Supabase Auth
•	Database access restricted by Row-Level Security policies
•	File access controlled via signed URLs with expiration
•	Payment information handled exclusively by Flutterwave
•	Input sanitization on all user-submitted data

*NOTE* All these Can Be edited as required, as development progresses.
