# Theme System Documentation

This document provides a comprehensive overview of the theme system implemented in AjiraPro, including the dark/light mode toggle, styling approach, and component theming.

## Table of Contents
1. [Overview](#overview)
2. [Theme Context](#theme-context)
3. [Theme Toggle Component](#theme-toggle-component)
4. [Tailwind Configuration](#tailwind-configuration)
5. [Component Theming](#component-theming)
6. [Best Practices](#best-practices)
7. [Technical Implementation](#technical-implementation)

## Overview

AjiraPro implements a comprehensive theme system that supports both light and dark modes. The theme system is built using React Context API for state management and Tailwind CSS for styling. The theme preference is persisted in localStorage and respects the user's system preference by default.

## Theme Context

The `ThemeContext` provides theme state and methods throughout the application:

```typescript
interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  isDark: boolean;
}
```

### Key Features

- **Theme State**: Tracks whether the current theme is 'light' or 'dark'
- **Toggle Function**: Allows switching between themes
- **isDark Helper**: Boolean flag for conditional rendering
- **System Preference Detection**: Automatically detects and applies the user's system preference
- **Persistence**: Saves theme preference to localStorage
- **Document Class**: Applies the theme class to the document element for global CSS

## Theme Toggle Component

The `ThemeToggle` component provides a user interface for switching between light and dark modes:

```tsx
const ThemeToggle: React.FC = () => {
  const { toggleTheme, isDark } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className="p-2 rounded-full focus:outline-none"
      whileTap={{ scale: 0.9 }}
      whileHover={{ scale: 1.1 }}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {/* Sun/Moon icons with animations */}
    </motion.button>
  );
};
```

### Key Features

- **Animated Icons**: Smooth transitions between sun and moon icons
- **Accessibility**: Proper aria-labels for screen readers
- **Motion Effects**: Subtle animations for better user experience
- **Positioning**: Placed in the header for easy access

## Tailwind Configuration

The theme system is built on Tailwind CSS with custom configuration:

```javascript
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        dark: {
          DEFAULT: '#121212',
          100: '#181818',
          200: '#202020',
          300: '#303030',
          400: '#404040',
          500: '#505050',
        },
        neon: {
          cyan: '#00FFFF',
          magenta: '#FF00FF',
          blue: '#00BFFF',
          purple: '#9D00FF',
        },
      },
      animation: {
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        glow: {
          '0%': { textShadow: '0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)' },
          '100%': { textShadow: '0 0 10px rgba(0, 255, 255, 0.8), 0 0 20px rgba(0, 255, 255, 0.5), 0 0 30px rgba(0, 255, 255, 0.3)' },
        },
      },
    },
  },
  plugins: [],
}
```

### Key Features

- **Class-based Dark Mode**: Uses the 'class' strategy for better control
- **Custom Color Palette**: Dark mode grays and neon accent colors
- **Custom Animations**: Glow effects for dark mode accents
- **Consistent Scaling**: Standardized color steps for dark mode

## Component Theming

Components in AjiraPro are designed to adapt to the current theme:

### Button Component

```tsx
const variantClasses = {
  primary: isDark
    ? 'bg-dark-300 text-neon-cyan border border-neon-cyan hover:bg-dark-400 active:bg-dark-500 shadow-[0_0_10px_rgba(0,255,255,0.3)]'
    : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md',
  secondary: isDark
    ? 'bg-dark-400 text-neon-magenta border border-neon-magenta hover:bg-dark-500 active:bg-dark-300 shadow-[0_0_10px_rgba(255,0,255,0.3)]'
    : 'bg-gray-200 text-gray-800 hover:bg-gray-300 active:bg-gray-400',
  // ...other variants
};
```

### Card Component

```tsx
const baseClasses = `rounded-lg overflow-hidden ${
  isDark
    ? 'bg-dark-200 border border-dark-300 shadow-[0_4px_20px_rgba(0,0,0,0.3)]'
    : 'bg-white border border-gray-200 shadow-md'
} transition-all duration-300`;
```

### Form Elements

```tsx
const inputClasses = `mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm ${
  isDark
    ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500 focus:ring-neon-cyan focus:border-neon-cyan'
    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
}`;
```

## Best Practices

When implementing theme support in components:

1. **Always Use the Theme Context**:
   ```tsx
   const { isDark } = useTheme();
   ```

2. **Conditional Styling**:
   ```tsx
   className={`${isDark ? 'dark-mode-class' : 'light-mode-class'}`}
   ```

3. **Consistent Color Palette**:
   - Light mode: Blues for primary, grays for secondary
   - Dark mode: Neon cyan for primary, dark grays for backgrounds

4. **Text Contrast**:
   - Light mode: Dark text on light backgrounds
   - Dark mode: Light text on dark backgrounds

5. **Visual Hierarchy**:
   - Use shadows in light mode
   - Use borders and glows in dark mode

6. **Transitions**:
   - Add `transition-colors duration-300` for smooth theme changes

7. **Testing**:
   - Test all components in both light and dark modes

## Technical Implementation

### Theme Provider Setup

The `ThemeProvider` is set up at the application root:

```tsx
function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Router>
          <div className="min-h-screen flex flex-col transition-colors duration-300 dark:bg-dark-100 dark:text-white">
            <Header />
            <main className="flex-grow">
              {/* Routes */}
            </main>
            <Footer />
          </div>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}
```

### Theme Context Implementation

```typescript
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Check if user has a theme preference in localStorage or prefers dark mode
  const getInitialTheme = (): Theme => {
    if (typeof window !== 'undefined' && window.localStorage) {
      const storedTheme = window.localStorage.getItem('theme') as Theme | null;
      if (storedTheme) {
        return storedTheme;
      }

      // Check user preference
      const userPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      return userPrefersDark ? 'dark' : 'light';
    }
    return 'light';
  };

  const [theme, setTheme] = useState<Theme>(getInitialTheme);
  const isDark = theme === 'dark';

  // Update the theme in localStorage and apply the class to the document
  useEffect(() => {
    const root = window.document.documentElement;
    
    // Remove the previous theme class
    root.classList.remove('light', 'dark');
    
    // Add the current theme class
    root.classList.add(theme);
    
    // Save the theme preference to localStorage
    localStorage.setItem('theme', theme);
  }, [theme]);

  // Toggle between light and dark themes
  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  // Create the context value
  const value = {
    theme,
    toggleTheme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
```

## Conclusion

The theme system in AjiraPro provides a seamless experience for users who prefer light or dark mode. It respects system preferences, persists user choices, and provides consistent styling across the application. The implementation uses modern React patterns and Tailwind CSS for efficient and maintainable code.
