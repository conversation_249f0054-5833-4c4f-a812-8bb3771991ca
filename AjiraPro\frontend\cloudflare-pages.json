{"build": {"command": "npm install --legacy-peer-deps && npm run build", "output_directory": "build", "root_directory": "AjiraPro/frontend"}, "routes": [{"pattern": "/static/*", "cache": {"browser_ttl": 31536000, "edge_ttl": 31536000}}, {"pattern": "/*.ico", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.png", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.svg", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.jpg", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.jpeg", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.webp", "cache": {"browser_ttl": 86400, "edge_ttl": 86400}}, {"pattern": "/*.html", "cache": {"browser_ttl": 0, "edge_ttl": 0}}, {"pattern": "/*", "cache": {"browser_ttl": 0, "edge_ttl": 0}}]}