-- Add Gemini validation columns to resumes table
-- Run this migration on Supabase dashboard

-- Add validation status tracking
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS validation_status VARCHAR(20) DEFAULT 'pending';

-- Add validation results
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS is_resume BOOLEAN DEFAULT NULL;
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS validation_likelihood INTEGER DEFAULT NULL;
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS validation_reason TEXT DEFAULT NULL;
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS validation_error TEXT DEFAULT NULL;
ALTER TABLE resumes ADD COLUMN IF NOT EXISTS validated_at TIMESTAMP DEFAULT NULL;

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_resumes_validation_status ON resumes(validation_status);

-- Update existing records to have pending validation status
UPDATE resumes SET validation_status = 'pending' WHERE validation_status IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN resumes.validation_status IS 'Gemini validation status: pending, validating, completed, failed';
COMMENT ON COLUMN resumes.is_resume IS 'Gemini validation result: true if document is a resume, false if not';
COMMENT ON COLUMN resumes.validation_likelihood IS 'Gemini confidence percentage (0-100)';
COMMENT ON COLUMN resumes.validation_reason IS 'Gemini explanation for validation result';
COMMENT ON COLUMN resumes.validation_error IS 'Error message if validation failed';
COMMENT ON COLUMN resumes.validated_at IS 'Timestamp when validation completed';
