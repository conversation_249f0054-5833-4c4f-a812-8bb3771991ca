import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

interface ProgressBarProps {
  value: number;
  max?: number;
  label?: string;
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'success' | 'warning' | 'danger';
  animated?: boolean;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  label,
  showValue = false,
  size = 'md',
  color = 'primary',
  animated = true,
  className = '',
}) => {
  const { isDark } = useTheme();
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100);

  // Size classes
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-4',
  };

  // Color classes based on theme
  const colorClasses = {
    primary: isDark
      ? 'bg-gradient-to-r from-neon-blue to-neon-cyan'
      : 'bg-blue-600',
    success: isDark
      ? 'bg-gradient-to-r from-green-400 to-green-500'
      : 'bg-green-500',
    warning: isDark
      ? 'bg-gradient-to-r from-yellow-400 to-yellow-500'
      : 'bg-yellow-500',
    danger: isDark
      ? 'bg-gradient-to-r from-red-500 to-neon-magenta'
      : 'bg-red-500',
  };

  // Background classes
  const bgClasses = isDark ? 'bg-dark-300' : 'bg-gray-200';

  return (
    <div className={`w-full ${className}`}>
      {(label || showValue) && (
        <div className="flex justify-between mb-1">
          {label && (
            <span className={`text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              {label}
            </span>
          )}
          {showValue && (
            <span className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              {value}/{max}
            </span>
          )}
        </div>
      )}
      <div className={`w-full ${sizeClasses[size]} ${bgClasses} rounded-full overflow-hidden`}>
        <motion.div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full ${
            animated && isDark ? 'shadow-[0_0_10px_rgba(0,255,255,0.5)]' : ''
          }`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        />
      </div>
      {animated && (
        <motion.div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full absolute top-0 left-0 opacity-30`}
          initial={{ width: 0 }}
          animate={{ 
            width: [`${percentage}%`, `${percentage + 5}%`, `${percentage}%`],
            opacity: [0.3, 0.1, 0.3]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            ease: 'easeInOut'
          }}
          style={{ width: `${percentage}%` }}
        />
      )}
    </div>
  );
};

export default ProgressBar;
