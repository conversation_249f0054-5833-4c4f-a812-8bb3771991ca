# Dependencies
node_modules/
venv/
__pycache__/
*.py[cod]
*$py.class

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build files
/frontend/build
/frontend/dist
/frontend/.next
/frontend/out

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Python
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
/coverage
.pytest_cache/
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover

# Supabase
.supabase/
