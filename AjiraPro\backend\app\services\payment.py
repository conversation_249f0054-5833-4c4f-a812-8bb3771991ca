from fastapi import Depends
from typing import List, Optional
from ..models.payment import PaymentCreate, PaymentResponse
from ..models.user import User
from .supabase import supabase
from ..core.config import settings
import uuid
import requests
from datetime import datetime

class PaymentService:
    async def initiate_payment(self, payment: PaymentCreate, user: User) -> Optional[str]:
        """
        Initiate a payment transaction with Flutterwave.
        """
        # Generate a unique transaction reference
        tx_ref = f"ajirapro-{uuid.uuid4()}"
        
        # Create a payment record in the database
        payment_data = {
            "user_id": str(user.id),
            "amount": payment.amount,
            "currency": payment.currency,
            "payment_type": payment.payment_type,
            "plan_id": payment.plan_id,
            "transaction_id": tx_ref,
            "status": "pending"
        }
        
        supabase.table("payments").insert(payment_data).execute()
        
        # Prepare the Flutterwave payment request
        flutterwave_url = "https://api.flutterwave.com/v3/payments"
        
        redirect_url = payment.redirect_url or f"{settings.FRONTEND_URL}/payment/callback"
        
        payload = {
            "tx_ref": tx_ref,
            "amount": payment.amount,
            "currency": payment.currency,
            "redirect_url": redirect_url,
            "customer": {
                "email": user.email,
                "name": user.full_name or user.email
            },
            "customizations": {
                "title": "AjiraPro Payment",
                "description": f"Payment for {payment.payment_type}",
                "logo": f"{settings.FRONTEND_URL}/logo.png"
            }
        }
        
        headers = {
            "Authorization": f"Bearer {settings.FLUTTERWAVE_SECRET_KEY}",
            "Content-Type": "application/json"
        }
        
        # In a real implementation, we would make the API call to Flutterwave
        # For now, we'll simulate the response
        if settings.ENVIRONMENT == "production":
            try:
                response = requests.post(flutterwave_url, json=payload, headers=headers)
                response_data = response.json()
                
                if response.status_code == 200 and response_data.get("status") == "success":
                    return response_data.get("data", {}).get("link")
                return None
            except Exception as e:
                print(f"Error initiating payment: {str(e)}")
                return None
        else:
            # For development, return a mock payment link
            return f"https://flutterwave.com/pay/ajirapro-mock?tx_ref={tx_ref}"

    async def verify_payment(self, transaction_id: str, user_id: uuid.UUID) -> Optional[PaymentResponse]:
        """
        Verify a payment transaction.
        """
        # In a real implementation, we would verify the payment with Flutterwave
        # For now, we'll simulate the verification
        
        # Check if the payment exists in our database
        response = supabase.table("payments").select("*").eq("transaction_id", transaction_id).eq("user_id", str(user_id)).execute()
        
        if len(response.data) == 0:
            return None
        
        payment_data = response.data[0]
        
        # Update the payment status to completed
        updated_data = {
            "status": "completed",
            "updated_at": datetime.now().isoformat()
        }
        
        update_response = supabase.table("payments").update(updated_data).eq("id", payment_data["id"]).execute()
        
        if len(update_response.data) > 0:
            # If this is a subscription payment, update the user's premium status
            if payment_data["payment_type"] == "subscription":
                supabase.table("profiles").update({"is_premium": True}).eq("id", str(user_id)).execute()
            
            return PaymentResponse(**update_response.data[0])
        
        return None

    async def get_payments(self, user_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[PaymentResponse]:
        """
        Get all payments for a user.
        """
        response = supabase.table("payments").select("*").eq("user_id", str(user_id)).range(skip, skip + limit - 1).execute()
        
        payments = []
        for payment_data in response.data:
            payments.append(PaymentResponse(**payment_data))
        
        return payments

    async def get_payment(self, payment_id: str, user_id: uuid.UUID) -> Optional[PaymentResponse]:
        """
        Get a specific payment by ID.
        """
        response = supabase.table("payments").select("*").eq("id", payment_id).eq("user_id", str(user_id)).execute()
        
        if len(response.data) > 0:
            return PaymentResponse(**response.data[0])
        
        return None

    async def process_webhook(self, payload: dict) -> None:
        """
        Process a webhook notification from Flutterwave.
        """
        # In a real implementation, we would verify the webhook signature
        # and process the payment notification
        
        # Extract the transaction reference
        tx_ref = payload.get("txRef")
        
        if not tx_ref:
            return
        
        # Check if the payment exists in our database
        response = supabase.table("payments").select("*").eq("transaction_id", tx_ref).execute()
        
        if len(response.data) == 0:
            return
        
        payment_data = response.data[0]
        
        # Update the payment status based on the webhook data
        status = "completed" if payload.get("status") == "successful" else "failed"
        
        updated_data = {
            "status": status,
            "updated_at": datetime.now().isoformat()
        }
        
        supabase.table("payments").update(updated_data).eq("id", payment_data["id"]).execute()
        
        # If this is a subscription payment and it was successful, update the user's premium status
        if payment_data["payment_type"] == "subscription" and status == "completed":
            supabase.table("profiles").update({"is_premium": True}).eq("id", payment_data["user_id"]).execute()
