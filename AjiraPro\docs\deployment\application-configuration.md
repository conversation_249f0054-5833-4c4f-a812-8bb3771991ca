# Application Configuration for Domain Integration

This document explains how the AjiraPro application is configured to work with the domain ajirapro.com.

## Frontend Configuration

### Environment Variables

The frontend application uses environment variables to configure domain-specific settings:

**Production Environment (.env.production)**:
```
REACT_APP_API_URL=https://api.ajirapro.com
REACT_APP_SUPABASE_URL=https://xmmbjopzpyotkyyfqzyq.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Development Environment (.env.development)**:
```
REACT_APP_API_URL=http://localhost:8000
REACT_APP_SUPABASE_URL=https://xmmbjopzpyotkyyfqzyq.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### API Service Configuration

The frontend application uses an API service to communicate with the backend. This service is configured to use the appropriate API URL based on the environment:

```typescript
// src/services/api.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default apiClient;
```

### Routing Configuration

The frontend application uses client-side routing with React Router. The routes are configured to work with the domain:

```typescript
// src/App.tsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/resume-builder" element={<ResumeBuilderPage />} />
        {/* Other routes */}
      </Routes>
    </Router>
  );
}
```

### Cloudflare Pages Configuration

The frontend application is deployed to Cloudflare Pages with the following configuration:

- **Custom Domain**: ajirapro.com, www.ajirapro.com
- **Build Command**: `cd AjiraPro/frontend && npm install --legacy-peer-deps && npm run build`
- **Build Output Directory**: `AjiraPro/frontend/build`
- **Environment Variables**: Set in Cloudflare Pages dashboard

## Backend Configuration

### Environment Variables

The backend application uses environment variables to configure domain-specific settings:

**Production Environment (.env.production)**:
```
FRONTEND_URL=https://ajirapro.com
CORS_ORIGINS=https://ajirapro.com,https://www.ajirapro.com
SUPABASE_URL=https://xmmbjopzpyotkyyfqzyq.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key
```

**Development Environment (.env.development)**:
```
FRONTEND_URL=http://localhost:3000
CORS_ORIGINS=http://localhost:3000
SUPABASE_URL=https://xmmbjopzpyotkyyfqzyq.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key
```

### CORS Configuration

The backend application is configured to allow cross-origin requests from the frontend domain:

```python
# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os

app = FastAPI()

# Configure CORS
origins = os.getenv("CORS_ORIGINS", "").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Railway Configuration (Future)

The backend application will be deployed to Railway with the following configuration:

- **Custom Domain**: api.ajirapro.com
- **Environment Variables**: Set in Railway dashboard
- **Build Command**: `pip install -r requirements.txt`
- **Start Command**: `uvicorn app.main:app --host 0.0.0.0 --port $PORT`

## Supabase Configuration

### URL Configuration

The Supabase project is configured to work with the domain:

- **Auth Redirect URLs**: https://ajirapro.com/auth/callback, http://localhost:3000/auth/callback
- **Auth Site URL**: https://ajirapro.com

### Security Configuration

The Supabase project is configured with security settings for the domain:

- **Row Level Security (RLS)**: Enabled for all tables
- **API Keys**: Restricted to specific domains (ajirapro.com, localhost)
- **Storage Buckets**: Configured with appropriate CORS settings

## DNS Configuration

The DNS records are configured to point to the appropriate services:

- **ajirapro.com** -> Cloudflare Pages (Frontend)
- **www.ajirapro.com** -> Cloudflare Pages (Frontend)
- **api.ajirapro.com** -> Railway (Backend - Future)

## SSL/TLS Configuration

SSL/TLS is configured to ensure secure communication:

- **Frontend**: SSL managed by Cloudflare
- **Backend**: SSL managed by Railway (Future)
- **API Communication**: All API calls use HTTPS

## Monitoring and Logging

The application is configured with monitoring and logging for the domain:

- **Frontend Monitoring**: Cloudflare Analytics
- **Backend Monitoring**: Railway Metrics (Future)
- **Error Tracking**: To be implemented
- **Performance Monitoring**: To be implemented

## Deployment Process

The deployment process is configured to work with the domain:

1. Code is pushed to the main branch
2. GitHub Actions workflow is triggered
3. Frontend is built and deployed to Cloudflare Pages
4. Backend is built and deployed to Railway (Future)
5. DNS records are automatically updated

## Testing Domain Configuration

To test the domain configuration:

1. **Frontend**: Visit https://ajirapro.com and verify the application loads correctly
2. **API**: Make a request to https://api.ajirapro.com/health (Future) and verify the response
3. **Authentication**: Test login functionality and verify redirect URLs work correctly

## Troubleshooting

### Common Issues

- **CORS Errors**: Verify CORS configuration in the backend
- **API Connection Issues**: Check environment variables and DNS configuration
- **Authentication Redirect Issues**: Verify Supabase auth configuration

### Checking Configuration

- **Frontend Environment**: Check environment variables in Cloudflare Pages dashboard
- **Backend Environment**: Check environment variables in Railway dashboard (Future)
- **DNS Configuration**: Check DNS records in Cloudflare dashboard
