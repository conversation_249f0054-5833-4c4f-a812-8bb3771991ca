[build]
builder = "nixpacks"

[deploy]
startCommand = "python start.py"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

# Worker service overrides (use environment variables to override these)
# For worker service, set these environment variables:
# RAILWAY_DOCKERFILE_PATH=Dockerfile.worker
# RAILWAY_START_COMMAND=python start_worker.py
# RAILWAY_HEALTHCHECK_PATH= (empty to disable)
# RAILWAY_HEALTHCHECK_TIMEOUT= (empty to disable)
