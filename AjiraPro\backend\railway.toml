# =============================================================================
# Railway Configuration for FajiraPro Backend
# =============================================================================

# Default configuration for FastAPI service
[build]
builder = "dockerfile"
dockerTarget = "fastapi"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

# =============================================================================
# Worker Service Configuration
# =============================================================================
# For the worker service, override these settings with environment variables:
#
# RAILWAY_DOCKER_TARGET=worker
# RAILWAY_HEALTHCHECK_PATH= (empty to disable health checks)
# RAILWAY_HEALTHCHECK_TIMEOUT= (empty to disable)
#
# The multi-stage Dockerfile will handle the different start commands automatically
