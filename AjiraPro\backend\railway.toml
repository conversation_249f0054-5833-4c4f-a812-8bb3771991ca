# =============================================================================
# Railway Configuration for FajiraPro Backend
# =============================================================================

[build]
builder = "dockerfile"
# dockerTarget is controlled by RAILWAY_DOCKER_TARGET environment variable
# FastAPI service: RAILWAY_DOCKER_TARGET=fastapi (or unset - defaults to fastapi)
# Worker service: RAILWAY_DOCKER_TARGET=worker

[deploy]
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3
# Health check settings are controlled by environment variables:
# FastAPI service: RAILWAY_HEALTHCHECK_PATH=/health, RAILWAY_HEALTHCHECK_TIMEOUT=300
# Worker service: RAILWAY_HEALTHCHECK_PATH= (empty), RAILWAY_HEALTHCHECK_TIMEOUT= (empty)
