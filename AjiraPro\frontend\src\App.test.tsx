import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

test('renders AI-Powered Resume Builder heading', () => {
  render(<App />);
  const headingElement = screen.getByText(/AI-Powered Resume Builder/i);
  expect(headingElement).toBeInTheDocument();
});

test('renders Get Started button', () => {
  render(<App />);
  const buttonElement = screen.getByText(/Get Started/i);
  expect(buttonElement).toBeInTheDocument();
});
