"""
Resume Analysis Celery Tasks

Handles asynchronous processing of resume parsing and scoring
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID

from celery import current_task
from ..celery_worker import celery
from ..services.supabase import supabase
from ..services.document_processor import document_processor
from ..services.claude_parser import claude_parser
from ..services.resume_validator import ai_resume_validator

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def parse_resume_task(self, resume_id: str, file_path: str):
    """
    Parse resume content using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
        file_path: Path to the resume file in Supabase storage
    """
    try:
        logger.info(f"Starting resume parsing for resume_id: {resume_id}")

        # Update status to validating first
        supabase.table("resumes").update({
            "validation_status": "validating"
        }).eq("id", resume_id).execute()

        # Download file from Supabase storage
        logger.info(f"Downloading file from Supabase: {file_path}")
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Fast AI-based resume validation using Gemini 1.5 Flash
        logger.info(f"Validating document as resume using Gemini AI: {file_path}")
        filename = file_path.split('/')[-1]  # Get filename from path

        try:
            # Check if GEMINI_API_KEY is available
            from ..core.config import settings
            if settings.GEMINI_API_KEY:
                logger.info("GEMINI_API_KEY found, attempting Gemini validation")
                validation_result = ai_resume_validator.validate_document_as_resume(
                    file_response, filename
                )

                logger.info(f"Gemini validation completed: {validation_result}")

                # Store validation results in database
                validation_update = {
                    "validation_status": "completed",
                    "is_resume": validation_result["is_resume"],
                    "validation_likelihood": validation_result["likelihood"],
                    "validation_reason": validation_result["reason"],
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }
                supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()

                # Check if document is a resume
                if not validation_result["is_resume"]:
                    logger.warning(f"Document is not a resume: {validation_result['reason']}")
                    # Update parsing status to indicate we're stopping here
                    supabase.table("resumes").update({
                        "parsing_status": "skipped"
                    }).eq("id", resume_id).execute()

                    return {
                        "status": "not_resume",
                        "resume_id": resume_id,
                        "reason": validation_result["reason"],
                        "likelihood": validation_result["likelihood"]
                    }

                logger.info(
                    f"Document validated as resume: {validation_result['likelihood']}% likelihood"
                )
            else:
                logger.warning("GEMINI_API_KEY not found, using fallback validation")
                # Use simple fallback validation
                validation_result = {
                    "is_resume": True,
                    "likelihood": 95,
                    "reason": "Fallback validation - GEMINI_API_KEY not configured"
                }

                # Store fallback validation results
                validation_update = {
                    "validation_status": "completed",
                    "is_resume": True,
                    "validation_likelihood": 95,
                    "validation_reason": "Fallback validation - GEMINI_API_KEY not configured",
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }
                supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()
                logger.info(f"Using fallback validation: {validation_result}")

        except Exception as validation_error:
            logger.error(f"Gemini validation failed: {str(validation_error)}")

            # Store validation error in database
            validation_update = {
                "validation_status": "failed",
                "validation_error": str(validation_error),
                "validated_at": datetime.now().isoformat()
            }
            supabase.table("resumes").update(validation_update).eq("id", resume_id).execute()

            # Use fallback validation to continue processing
            validation_result = {
                "is_resume": True,
                "likelihood": 90,
                "reason": "Fallback validation - Gemini validation failed"
            }

            # Update with fallback results
            fallback_update = {
                "validation_status": "completed",
                "is_resume": True,
                "validation_likelihood": 90,
                "validation_reason": "Fallback validation - Gemini validation failed"
            }
            supabase.table("resumes").update(fallback_update).eq("id", resume_id).execute()
            logger.warning(f"Using fallback validation due to error: {validation_result}")

        # Now proceed with parsing (validation completed)
        logger.info(f"Validation completed, proceeding with parsing for resume {resume_id}")

        # Update status to parsing
        supabase.table("resumes").update({
            "parsing_status": "parsing"
        }).eq("id", resume_id).execute()

        # Extract full text for Claude parsing (only after validation)
        logger.info(f"Extracting full text from validated resume: {file_path}")
        extracted_data = document_processor.extract_text_from_file(file_response, filename)

        # Parse resume using Claude
        logger.info(f"Parsing resume content with Claude for resume_id: {resume_id}")
        parsed_content = asyncio.run(claude_parser.parse_resume_content(
            extracted_data["text"],
            filename
        ))

        # Update resume with parsed content
        update_data = {
            "parsed_content": json.dumps(parsed_content),
            "parsing_status": "parsed",
            "parsed_at": datetime.now().isoformat(),
            "parsing_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully parsed resume {resume_id}")

        # Queue format scoring task
        score_format_task.delay(resume_id)

        return {
            "status": "success",
            "resume_id": resume_id,
            "parsed_sections": list(parsed_content.keys())
        }

    except Exception as e:
        logger.error(f"Resume parsing task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "parsing_status": "failed",
            "parsing_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def score_format_task(self, resume_id: str):
    """
    Score resume format and structure using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting format scoring for resume_id: {resume_id}")

        # Update scoring status
        supabase.table("resumes").update({
            "scoring_status": "scoring"
        }).eq("id", resume_id).execute()

        # Get resume data including parsed content
        resume_response = supabase.table("resumes").select(
            "parsed_content, file_path"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        parsed_content = json.loads(resume_data["parsed_content"])

        # Download original file to get text for format analysis
        file_path = resume_data["file_path"]
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract text from document
        filename = file_path.split('/')[-1]
        extracted_data = document_processor.extract_text_from_file(file_response, filename)
        original_text = extracted_data["text"]

        # Score format using Claude
        logger.info(f"Scoring format with Claude for resume_id: {resume_id}")
        claude_score, claude_feedback = asyncio.run(claude_parser.score_format_and_structure(
            parsed_content,
            original_text
        ))

        # Update resume with Claude scoring results
        update_data = {
            "claude_score": claude_score,
            "claude_feedback": json.dumps(claude_feedback),
            "scoring_status": "scored",
            "last_scored_at": datetime.now().isoformat(),
            "scoring_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully scored format for resume {resume_id}, score: {claude_score}")

        return {
            "status": "success",
            "resume_id": resume_id,
            "claude_score": claude_score,
            "feedback_items": len(claude_feedback.get("format_issues", []))
        }

    except Exception as e:
        logger.error(f"Format scoring task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "scoring_status": "failed",
            "scoring_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True)
def analyze_resume_task(self, resume_id: str):
    """
    Complete resume analysis workflow - parsing and scoring

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting complete analysis for resume_id: {resume_id}")

        # Get resume record to get file path
        resume_response = supabase.table("resumes").select("file_path").eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        file_path = resume_response.data[0]["file_path"]
        if not file_path:
            raise Exception(f"No file path found for resume {resume_id}")

        # Start with parsing
        parse_result = parse_resume_task.delay(resume_id, file_path)

        return {
            "status": "started",
            "resume_id": resume_id,
            "parse_task_id": parse_result.id
        }

    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise

@celery.task
def get_analysis_status(resume_id: str) -> Dict[str, Any]:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume record

    Returns:
        Dict with current status information
    """
    try:
        resume_response = supabase.table("resumes").select(
            "validation_status, is_resume, validation_reason, parsing_status, scoring_status, validated_at, parsed_at, last_scored_at, parsing_error, scoring_error, validation_error"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            return {"error": f"Resume {resume_id} not found"}

        data = resume_response.data[0]

        return {
            "resume_id": resume_id,
            "validation_status": data.get("validation_status"),
            "is_resume": data.get("is_resume"),
            "validation_reason": data.get("validation_reason"),
            "parsing_status": data["parsing_status"],
            "scoring_status": data["scoring_status"],
            "validated_at": data.get("validated_at"),
            "parsed_at": data["parsed_at"],
            "last_scored_at": data["last_scored_at"],
            "parsing_error": data["parsing_error"],
            "scoring_error": data["scoring_error"],
            "validation_error": data.get("validation_error"),
            "overall_status": _determine_overall_status(data)
        }

    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        return {"error": str(e)}

def _determine_overall_status(data: Dict[str, Any]) -> str:
    """Determine overall analysis status based on validation, parsing and scoring status"""
    validation_status = data.get("validation_status", "pending")
    is_resume = data.get("is_resume")
    parsing_status = data.get("parsing_status", "pending")
    scoring_status = data.get("scoring_status", "pending")

    # Validation phase
    if validation_status == "pending":
        return "pending"
    elif validation_status == "validating":
        return "validating"
    elif validation_status == "failed":
        return "validation_failed"
    elif validation_status == "completed" and not is_resume:
        return "not_resume"

    # Parsing phase (only if validation passed and is_resume = true)
    if parsing_status == "failed":
        return "failed"
    elif parsing_status == "parsing":
        return "parsing"
    elif parsing_status == "skipped":
        return "not_resume"
    elif parsing_status == "parsed" and scoring_status == "pending":
        return "ready_for_scoring"

    # Scoring phase
    elif scoring_status == "scoring":
        return "scoring"
    elif scoring_status == "scored":
        return "completed"
    elif scoring_status == "failed":
        return "scoring_failed"
    else:
        return "pending"
