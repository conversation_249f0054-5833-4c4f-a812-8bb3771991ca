# AjiraPro Architecture Overview

This document provides a high-level overview of the AjiraPro application architecture.

## System Architecture

AjiraPro follows a modern, cloud-native architecture with the following key components:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client (Browser)                         │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Cloudflare (DNS, CDN)                      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend (React)                         │
│                                                                 │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────────┐    │
│  │    Pages    │   │  Components │   │      Services       │    │
│  └─────────────┘   └─────────────┘   └─────────────────────┘    │
│                                                                 │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Backend (FastAPI)                        │
│                                                                 │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────────┐    │
│  │   Routers   │   │    Models   │   │      Services       │    │
│  └─────────────┘   └─────────────┘   └─────────────────────┘    │
│                                                                 │
└───┬───────────────────┬───────────────────┬───────────────────┬─┘
    │                   │                   │                   │
    ▼                   ▼                   ▼                   ▼
┌─────────────┐   ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│  Supabase   │   │   OpenAI    │   │    Redis    │   │   Celery    │
│  Database   │   │     API     │   │   Cache     │   │   Tasks     │
└─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘
```

## Component Details

### Frontend Architecture

The frontend is built with React and TypeScript, following a component-based architecture:

1. **Pages**: Top-level components that represent different routes in the application
   - Home page
   - Resume builder
   - Resume tailoring
   - CV revamp
   - User dashboard

2. **Components**: Reusable UI elements
   - Form components
   - Resume templates
   - Navigation elements
   - Modals and dialogs

3. **Services**: Handle business logic and API communication
   - Authentication service
   - Resume service
   - User service
   - Payment service

4. **State Management**: Uses React Context API and hooks for state management
   - User context
   - Resume context
   - UI state context

### Backend Architecture

The backend is built with FastAPI (Python), following a modular architecture:

1. **Routers**: Define API endpoints and handle HTTP requests
   - Auth router
   - Resume router
   - User router
   - Payment router

2. **Models**: Define data structures and validation
   - User model
   - Resume model
   - Payment model
   - Job model

3. **Services**: Implement business logic
   - Resume generation service
   - ATS optimization service
   - PDF generation service
   - Payment processing service

4. **Dependencies**: Handle cross-cutting concerns
   - Authentication
   - Error handling
   - Logging
   - Rate limiting

### Data Architecture

1. **Database**: PostgreSQL (via Supabase)
   - Users table
   - Resumes table
   - Payments table
   - Templates table

2. **Storage**: Supabase Storage
   - Resume files bucket
   - User uploads bucket
   - Template assets bucket

3. **Caching**: Redis (to be implemented)
   - Session cache
   - Frequent queries cache
   - Rate limiting

### Integration Architecture

1. **External APIs**:
   - OpenAI API for content generation
   - Flutterwave API for payments
   - Email service API for notifications

2. **Authentication**:
   - Supabase Auth for user authentication
   - JWT tokens for API authentication
   - Role-based access control

## Key Workflows

### Resume Creation Workflow

1. User fills out resume information form
2. Frontend sends data to backend
3. Backend processes data and generates content using OpenAI
4. Backend formats content into selected template
5. Backend generates PDF document
6. Frontend displays preview and allows download

### Resume Tailoring Workflow

1. User uploads existing resume
2. User provides job description
3. Backend extracts content from resume
4. Backend analyzes job description for keywords and requirements
5. Backend generates tailored content using OpenAI
6. Backend creates new resume with tailored content
7. Frontend displays preview and allows download

### Payment Workflow

1. User selects a paid feature
2. Frontend initiates payment process
3. User completes payment through Flutterwave
4. Flutterwave sends webhook notification
5. Backend verifies payment and updates user access
6. Frontend unlocks paid feature

## Security Architecture

1. **Authentication**: JWT-based authentication through Supabase Auth
2. **Authorization**: Row-level security policies in Supabase
3. **Data Protection**: HTTPS for all communications
4. **Input Validation**: Request validation using Pydantic models
5. **Output Sanitization**: Proper encoding of user-generated content

## Performance Considerations

1. **Frontend Performance**:
   - Code splitting for faster initial load
   - Lazy loading of components
   - Optimized bundle size

2. **Backend Performance**:
   - Asynchronous request handling
   - Database query optimization
   - Caching of frequent operations

3. **Scalability**:
   - Stateless API design
   - Horizontal scaling capability
   - Background processing for long-running tasks

## Future Architecture Enhancements

1. **Microservices**: Split backend into specialized microservices
2. **Event-Driven Architecture**: Implement event bus for better decoupling
3. **GraphQL API**: Consider GraphQL for more efficient data fetching
4. **Edge Computing**: Leverage Cloudflare Workers for edge processing
5. **Machine Learning Pipeline**: Dedicated ML pipeline for ATS optimization
