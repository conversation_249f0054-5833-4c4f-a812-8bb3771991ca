import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../utils/supabase';
import { useTheme } from '../contexts/ThemeContext';
import { ActionButtons, DocumentActions, TableControls, Pagination } from '../components/dashboard';
import { CheckboxIcon } from '../components/ui/icons';
import PDFViewerModal from '../components/PDFViewerModal';
import DeleteConfirmationModal from '../components/DeleteConfirmationModal';

interface Resume {
  id: string;
  title: string;
  status: string;
  created_at: string;
  updated_at: string;
  file_path?: string;
}

// CV interface removed

const DashboardPage: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [resumes, setResumes] = useState<Resume[]>([]);
  // CV state removed
  // Tab state removed - only resumes now
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { isDark } = useTheme();

  // PDF Viewer Modal state
  const [pdfModalOpen, setPdfModalOpen] = useState(false);
  const [selectedPdfPath, setSelectedPdfPath] = useState('');
  const [selectedPdfName, setSelectedPdfName] = useState('');

  // Delete Confirmation Modal state
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteResumeId, setDeleteResumeId] = useState('');
  const [deleteResumeTitle, setDeleteResumeTitle] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser(user);
          await fetchUserContent(user.id);
        }
      } catch (error) {
        // If there's an error, we'll just show the login prompt
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const fetchUserContent = async (userId: string) => {
    try {
      // Fetch resumes
      const { data: resumeData, error: resumeError } = await supabase
        .from('resumes')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (resumeError) throw resumeError;
      setResumes(resumeData || []);

      // CV fetching removed
    } catch (error) {
      // If there's an error fetching content, we'll show empty lists
      setResumes([]);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadgeClass = (status: string) => {
    if (isDark) {
      switch (status) {
        case 'completed':
          return 'bg-green-900/30 text-green-400 border border-green-700';
        case 'pending_payment':
          return 'bg-yellow-900/30 text-yellow-400 border border-yellow-700';
        case 'in_progress':
          return 'bg-blue-900/30 text-blue-400 border border-blue-700';
        default:
          return 'bg-gray-800 text-gray-300 border border-gray-700';
      }
    } else {
      switch (status) {
        case 'completed':
          return 'bg-green-100 text-green-800';
        case 'pending_payment':
          return 'bg-yellow-100 text-yellow-800';
        case 'in_progress':
          return 'bg-blue-100 text-blue-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    }
  };

  // Tab change effect removed

  // Handle item selection
  const toggleItemSelection = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Handle select all items on current page
  const toggleSelectAll = (items: Resume[]) => {
    const currentItems = getCurrentPageItems(items).map(item => item.id);

    if (currentItems.every(id => selectedItems.includes(id))) {
      // If all are selected, unselect all
      setSelectedItems(prev => prev.filter(id => !currentItems.includes(id)));
    } else {
      // Otherwise, select all that aren't already selected
      const newSelectedItems = [...selectedItems];
      currentItems.forEach(id => {
        if (!newSelectedItems.includes(id)) {
          newSelectedItems.push(id);
        }
      });
      setSelectedItems(newSelectedItems);
    }
  };

  // Handle batch delete
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) return;

    const table = 'resumes';

    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .in('id', selectedItems);

      if (error) throw error;

      // Refresh data
      if (user) {
        await fetchUserContent(user.id);
      }

      // Clear selection
      setSelectedItems([]);
    } catch (error) {
      // Error handling
      // TODO: Show error notification
      // console.error('Error deleting items:', error);
    }
  };

  // Handle delete item (show confirmation modal)
  const handleDeleteItem = (id: string) => {
    const resume = resumes.find(r => r.id === id);
    if (resume) {
      setDeleteResumeId(id);
      setDeleteResumeTitle(resume.title);
      setDeleteModalOpen(true);
    }
  };

  // Handle confirmed delete
  const handleConfirmDelete = async () => {
    if (!deleteResumeId) return;

    setIsDeleting(true);
    try {
      // Delete from database
      const { error } = await supabase
        .from('resumes')
        .delete()
        .eq('id', deleteResumeId);

      if (error) {
        throw error;
      }

      // Refresh data
      if (user) {
        await fetchUserContent(user.id);
      }

      // Remove from selection if selected
      if (selectedItems.includes(deleteResumeId)) {
        setSelectedItems(prev => prev.filter(item => item !== deleteResumeId));
      }

      // Close modal and reset state
      setDeleteModalOpen(false);
      setDeleteResumeId('');
      setDeleteResumeTitle('');
    } catch (error) {
      // Error handling - could show error notification
      // For now, still close the modal even if there's an error
      setDeleteModalOpen(false);
      setDeleteResumeId('');
      setDeleteResumeTitle('');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle batch download
  const handleBatchDownload = () => {
    // TODO: Implement batch download functionality
    // console.log('Download selected items:', selectedItems);
  };

  // Handle view resume (open PDF modal)
  const handleViewResume = (id: string) => {
    const resume = resumes.find(r => r.id === id);
    if (resume && resume.file_path) {
      setSelectedPdfPath(resume.file_path);
      setSelectedPdfName(resume.title);
      setPdfModalOpen(true);
    }
  };

  // Handle download resume
  const handleDownloadResume = async (id: string) => {
    try {
      const resume = resumes.find(r => r.id === id);
      if (!resume || !resume.file_path) {
        // Resume file path not found - could show error notification
        return;
      }

      // Create signed URL for download using Supabase
      const { data, error } = await supabase.storage
        .from('resumes')
        .createSignedUrl(resume.file_path, 60); // 1 minute expiry for download

      if (error || !data?.signedUrl) {
        // Error creating signed URL - could show error notification
        return;
      }

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = data.signedUrl;
      link.download = `${resume.title}.pdf`;
      link.target = '_blank'; // Open in new tab as fallback
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      // Error downloading resume - could show error notification
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Filter items based on search query
  const filterItems = (items: Resume[]) => {
    if (!searchQuery.trim()) return items;

    const query = searchQuery.toLowerCase();
    return items.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.status.toLowerCase().includes(query)
    );
  };

  // Get current page items
  const getCurrentPageItems = (items: Resume[]) => {
    const filteredItems = filterItems(items);
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredItems.slice(indexOfFirstItem, indexOfLastItem);
  };

  // Calculate total pages
  const getTotalPages = (items: Resume[]) => {
    return Math.ceil(filterItems(items).length / itemsPerPage);
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} flex justify-center items-center`}>
        <div className={`animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 ${isDark ? 'border-neon-cyan' : 'border-blue-500'}`}></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} flex flex-col justify-center items-center p-4`}>
        <h2 className={`text-2xl font-bold mb-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>You need to be logged in to view this page</h2>
        <button
          onClick={() => window.location.href = '/login'}
          className={`${isDark ? 'bg-dark-400 hover:bg-dark-500 text-neon-cyan border border-neon-cyan' : 'bg-blue-600 hover:bg-blue-700 text-white'} font-bold py-2 px-4 rounded`}
        >
          Sign In
        </button>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'}`}>
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className={`${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow'} overflow-hidden sm:rounded-lg`}>
            <div className="px-4 py-5 sm:px-6">
              <h2 className={`text-lg leading-6 font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>Dashboard</h2>
              <p className={`mt-1 max-w-2xl text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                Welcome back, {user.user_metadata?.full_name || user.email}!
              </p>
            </div>

            <div className={`border-t ${isDark ? 'border-dark-400' : 'border-gray-200'}`}>
              <div className="px-4 py-5 sm:p-6">
                <div className="flex flex-col mb-6">
                  <div className="flex justify-between items-center mb-4">
                    <div className={`border-b ${isDark ? 'border-dark-400' : 'border-gray-200'}`}>
                      <nav className="-mb-px flex space-x-8">
                        <div
                          className={`${
                            isDark
                              ? 'border-neon-cyan text-neon-cyan'
                              : 'border-blue-500 text-blue-600'
                          } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                          Resumes ({filterItems(resumes).length})
                        </div>
                      </nav>
                    </div>

                    <ActionButtons tab="resumes" />
                  </div>

                  <TableControls
                    onSearch={handleSearch}
                    onBatchDelete={handleBatchDelete}
                    onBatchDownload={handleBatchDownload}
                    hasSelection={selectedItems.length > 0}
                  />
                </div>

                {(
                  <div>
                    {resumes.length === 0 ? (
                      <div className="text-center py-12">
                        <svg
                          className={`mx-auto h-12 w-12 ${isDark ? 'text-gray-600' : 'text-gray-400'}`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        <h3 className={`mt-2 text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>No resumes</h3>
                        <p className={`mt-1 text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                          Get started by optimizing your first resume.
                        </p>
                        <div className="mt-6">
                          <Link
                            to="/resume-optimizer"
                            className={`inline-flex items-center px-4 py-2 border shadow-sm text-sm font-medium rounded-md ${
                              isDark
                                ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                            } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                          >
                            Optimize Resume
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col">
                        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                            <div className={`shadow overflow-hidden border-b ${isDark ? 'border-dark-400' : 'border-gray-200'} sm:rounded-lg`}>
                              <div className="overflow-x-auto">
                                <table className={`min-w-full divide-y ${isDark ? 'divide-dark-400' : 'divide-gray-200'}`}>
                                <thead className={isDark ? 'bg-dark-300' : 'bg-gray-50'}>
                                  <tr>
                                    <th scope="col" className="px-3 py-3 w-10">
                                      <div className="flex items-center">
                                        <button
                                          onClick={() => toggleSelectAll(resumes)}
                                          className={`p-1 rounded-sm focus:outline-none ${
                                            isDark ? 'hover:bg-dark-400' : 'hover:bg-gray-200'
                                          }`}
                                          aria-label="Select all"
                                        >
                                          <CheckboxIcon
                                            className={`w-5 h-5 ${
                                              getCurrentPageItems(resumes).length > 0 &&
                                              getCurrentPageItems(resumes).every(item => selectedItems.includes(item.id))
                                                ? isDark ? 'text-neon-cyan' : 'text-blue-600'
                                                : isDark ? 'text-gray-500' : 'text-gray-400'
                                            }`}
                                            checked={
                                              getCurrentPageItems(resumes).length > 0 &&
                                              getCurrentPageItems(resumes).every(item => selectedItems.includes(item.id))
                                            }
                                          />
                                        </button>
                                      </div>
                                    </th>
                                    <th
                                      scope="col"
                                      className={`px-6 py-3 text-left text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}
                                    >
                                      Title
                                    </th>
                                    <th
                                      scope="col"
                                      className={`px-6 py-3 text-left text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}
                                    >
                                      Status
                                    </th>
                                    <th
                                      scope="col"
                                      className={`px-6 py-3 text-left text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider hidden md:table-cell`}
                                    >
                                      Last Updated
                                    </th>
                                    <th
                                      scope="col"
                                      className={`px-6 py-3 text-left text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider hidden lg:table-cell`}
                                    >
                                      Created
                                    </th>
                                    <th scope="col" className="relative px-6 py-3">
                                      <span className="sr-only">Actions</span>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className={`${isDark ? 'bg-dark-200 divide-y divide-dark-400' : 'bg-white divide-y divide-gray-200'}`}>
                                  {getCurrentPageItems(resumes).map((resume) => (
                                    <tr
                                      key={resume.id}
                                      className={`${
                                        selectedItems.includes(resume.id)
                                          ? isDark ? 'bg-dark-300' : 'bg-blue-50'
                                          : ''
                                      } hover:${isDark ? 'bg-dark-300' : 'bg-gray-50'} transition-colors duration-150 ${
                                        isDark ? 'hover:shadow-[0_0_8px_rgba(0,255,255,0.15)]' : 'hover:shadow-md'
                                      }`}
                                    >
                                      <td className="px-3 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                          <button
                                            onClick={() => toggleItemSelection(resume.id)}
                                            className={`p-1 rounded-sm focus:outline-none ${
                                              isDark ? 'hover:bg-dark-400' : 'hover:bg-gray-200'
                                            }`}
                                            aria-label={selectedItems.includes(resume.id) ? "Deselect" : "Select"}
                                          >
                                            <CheckboxIcon
                                              className={`w-5 h-5 ${
                                                selectedItems.includes(resume.id)
                                                  ? isDark ? 'text-neon-cyan' : 'text-blue-600'
                                                  : isDark ? 'text-gray-500' : 'text-gray-400'
                                              }`}
                                              checked={selectedItems.includes(resume.id)}
                                            />
                                          </button>
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <div className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                                          {resume.title}
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <span
                                          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(
                                            resume.status
                                          )}`}
                                        >
                                          {resume.status.replace('_', ' ')}
                                        </span>
                                      </td>
                                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'} hidden md:table-cell`}>
                                        {formatDate(resume.updated_at)}
                                      </td>
                                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'} hidden lg:table-cell`}>
                                        {formatDate(resume.created_at)}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-right">
                                        <DocumentActions
                                          id={resume.id}
                                          type="resume"
                                          status={resume.status}
                                          onDelete={handleDeleteItem}
                                          onView={handleViewResume}
                                          onDownload={handleDownloadResume}
                                        />
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                                </table>
                              </div>

                              {/* Pagination for resumes */}
                              {filterItems(resumes).length > 0 && (
                                <Pagination
                                  currentPage={currentPage}
                                  totalPages={getTotalPages(resumes)}
                                  onPageChange={(page) => setCurrentPage(page)}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* PDF Viewer Modal */}
      <PDFViewerModal
        isOpen={pdfModalOpen}
        onClose={() => setPdfModalOpen(false)}
        filePath={selectedPdfPath}
        fileName={selectedPdfName}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => {
          if (!isDeleting) {
            setDeleteModalOpen(false);
            setDeleteResumeId('');
            setDeleteResumeTitle('');
          }
        }}
        onConfirm={handleConfirmDelete}
        fileName={deleteResumeTitle}
        isDeleting={isDeleting}
      />
    </div>
  );
};

export default DashboardPage;
