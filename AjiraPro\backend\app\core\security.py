from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.services.supabase import supabase
from typing import Optional

# Security scheme for JWT authentication
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """
    Validate the JWT token and return the user information.

    Args:
        credentials: The JWT token from the Authorization header

    Returns:
        dict: The user information

    Raises:
        HTTPException: If the token is invalid or expired
    """
    try:
        # Extract the token from the Authorization header
        token = credentials.credentials

        # Verify the JWT token with Supabase
        response = supabase.auth.get_user(token)

        # If successful, return the user data as a dictionary
        user = response.user
        return {
            "id": user.id,
            "email": user.email,
            "created_at": user.created_at,
            "last_sign_in_at": user.last_sign_in_at
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[dict]:
    """
    Similar to get_current_user but doesn't raise an exception if no token is provided.

    Args:
        credentials: The JWT token from the Authorization header (optional)

    Returns:
        Optional[dict]: The user information or None if no valid token
    """
    if not credentials:
        return None

    try:
        token = credentials.credentials
        response = supabase.auth.get_user(token)
        user = response.user
        return {
            "id": user.id,
            "email": user.email,
            "created_at": user.created_at,
            "last_sign_in_at": user.last_sign_in_at
        }
    except Exception:
        return None
