# Domain Configuration for AjiraPro

This document provides detailed information about the domain configuration for the AjiraPro application.

## Domain Details

- **Primary Domain**: [ajirapro.com](https://ajirapro.com)
- **Registrar**: Cloudflare Registry
- **DNS Provider**: Cloudflare
- **SSL/TLS**: Managed by Cloudflare

## DNS Configuration

### Frontend (Cloudflare Pages)

| Type  | Name | Content                      | Proxy Status | TTL    |
|-------|------|------------------------------|--------------|--------|
| CNAME | www  | ajirapro.pages.dev          | Proxied      | Auto   |
| A     | @    | 192.0.2.1 (Cloudflare Pages) | Proxied      | Auto   |

### Backend (Railway - Future Setup)

| Type  | Name | Content                      | Proxy Status | TTL    |
|-------|------|------------------------------|--------------|--------|
| CNAME | api  | ajirapro-api.up.railway.app  | Proxied      | Auto   |

## SSL/TLS Configuration

- **Mode**: Full (Strict)
- **Certificate**: Managed by Cloudflare (Auto-renewing)
- **Edge Certificates**: Issued by Cloudflare
- **Origin Certificates**: Issued by Cloudflare for backend services

## Security Settings

- **HTTPS Redirect**: Enabled (Always use HTTPS)
- **TLS Version**: TLS 1.2 and TLS 1.3 only
- **HSTS**: Enabled with max-age of 1 year
- **Minimum TLS Version**: TLS 1.2

## Cloudflare Pages Configuration

- **Project Name**: ajirapro
- **Production Branch**: main
- **Custom Domain**: ajirapro.com, www.ajirapro.com
- **Environment Variables**: Set in Cloudflare Pages dashboard

## Subdomain Strategy

| Subdomain | Purpose                       | Service          |
|-----------|-------------------------------|------------------|
| www       | Main website                  | Cloudflare Pages |
| api       | Backend API                   | Railway          |
| docs      | Documentation (future)        | Cloudflare Pages |
| admin     | Admin dashboard (future)      | Cloudflare Pages |

## Email Configuration

- **MX Records**: Not yet configured
- **SPF Record**: Not yet configured
- **DKIM**: Not yet configured
- **DMARC**: Not yet configured

## Domain Renewal

- **Renewal Date**: [Add renewal date once domain is registered]
- **Auto-Renewal**: Enabled
- **Registrar Contact**: [Your contact email]

## Monitoring

- **Uptime Monitoring**: Cloudflare Health Checks
- **SSL Certificate Monitoring**: Cloudflare

## Future Domain Considerations

- **Internationalization**: Consider purchasing country-specific TLDs for international expansion
- **Brand Protection**: Consider purchasing similar domains to protect the brand
- **Subdomains**: Plan for additional subdomains as the application grows

## Domain Management Procedures

### Adding a New Subdomain

1. Log in to Cloudflare dashboard
2. Navigate to DNS settings for ajirapro.com
3. Add a new CNAME record for the subdomain
4. Configure the service to use the new subdomain
5. Update documentation

### Transferring Domain Ownership

1. Obtain authorization code from current registrar
2. Unlock domain at current registrar
3. Initiate transfer at new registrar
4. Approve transfer request
5. Verify DNS settings after transfer

### SSL Certificate Renewal

- SSL certificates are automatically renewed by Cloudflare
- No manual action required

## Troubleshooting

### Common DNS Issues

- **DNS Propagation**: Changes can take up to 48 hours to propagate globally
- **CNAME Flattening**: Cloudflare automatically flattens CNAME records at the apex domain
- **SSL Errors**: Ensure SSL/TLS mode is set correctly in Cloudflare

### Checking DNS Propagation

Use the following tools to check DNS propagation:
- [whatsmydns.net](https://www.whatsmydns.net/)
- [dnschecker.org](https://dnschecker.org/)

### Checking SSL Certificate

Use the following tools to check SSL certificate status:
- [ssllabs.com/ssltest](https://www.ssllabs.com/ssltest/)
- [whatsmychaincert.com](https://whatsmychaincert.com/)
