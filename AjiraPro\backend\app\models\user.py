from pydantic import BaseModel, Field
from typing import Annotated
from pydantic import EmailStr
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID

class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    profile_picture: Optional[str] = None
    phone_number: Optional[str] = None
    address: Optional[Dict[str, Any]] = None
    social_links: Optional[Dict[str, str]] = None

class User(UserBase):
    id: UUID
    is_active: bool = True
    is_premium: bool = False
    profile_picture: Optional[str] = None
    phone_number: Optional[str] = None
    address: Optional[Dict[str, Any]] = None
    social_links: Optional[Dict[str, str]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
