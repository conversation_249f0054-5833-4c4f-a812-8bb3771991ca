## Redis-jRgH b4b7e0e Build Log
redis 00:32:44.11 INFO  ==> 

redis 00:32:44.11 INFO  ==> Welcome to the Bitnami redis container

redis 00:32:44.11 INFO  ==> Subscribe to project updates by watching https://github.com/bitnami/containers

redis 00:32:44.11 INFO  ==> Submit issues and feature requests at https://github.com/bitnami/containers/issues

redis 00:32:44.11 INFO  ==> Upgrade to Tanzu Application Catalog for production environments to access custom-configured and pre-packaged software components. Gain enhanced features, including Software Bill of Materials (SBOM), CVE scan result reports, and VEX documents. To learn more, visit https://bitnami.com/enterprise

redis 00:32:44.11 INFO  ==> 

redis 00:32:44.12 INFO  ==> ** Starting Redis setup **

redis 00:32:44.18 INFO  ==> Initializing Redis

redis 00:32:44.20 INFO  ==> Setting Redis config file

redis 00:32:44.22 INFO  ==> ** Redis setup finished! **

 

redis 00:32:44.23 INFO  ==> ** Starting Redis **

1:C 06 Jun 2025 00:32:44.246 # WARNING Memory overcommit must be enabled! Without it, a background save or replication may fail under low memory condition. Being disabled, it can also cause failures without low memory condition, see https://github.com/jemalloc/jemalloc/issues/1328. To fix this issue add 'vm.overcommit_memory = 1' to /etc/sysctl.conf and then reboot or run the command 'sysctl vm.overcommit_memory=1' for this to take effect.

1:C 06 Jun 2025 00:32:44.246 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo

1:C 06 Jun 2025 00:32:44.246 * Redis version=7.2.5, bits=64, commit=00000000, modified=0, pid=1, just started

1:C 06 Jun 2025 00:32:44.246 * Configuration loaded

1:M 06 Jun 2025 00:32:44.247 * monotonic clock: POSIX clock_gettime

1:M 06 Jun 2025 00:32:44.247 * Running mode=standalone, port=6379.

1:M 06 Jun 2025 00:32:44.248 * Server initialized

1:M 06 Jun 2025 00:32:44.248 * Loading RDB produced by version 7.2.5

1:M 06 Jun 2025 00:32:44.248 * RDB age 5 seconds

1:M 06 Jun 2025 00:32:44.248 * RDB memory usage when created 1.40 Mb

1:M 06 Jun 2025 00:32:44.248 * Done loading RDB, keys loaded: 3, keys expired: 0.

1:M 06 Jun 2025 00:32:44.248 * DB loaded from disk: 0.000 seconds

1:M 06 Jun 2025 00:32:44.248 * Ready to accept connections tcp

1:M 06 Jun 2025 01:29:06.414 * 100 changes in 300 seconds. Saving...

1:M 06 Jun 2025 01:29:06.414 * Background saving started by pid 65

65:C 06 Jun 2025 01:29:06.422 * DB saved on disk

65:C 06 Jun 2025 01:29:06.422 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 01:29:06.516 * Background saving terminated with success

1:M 06 Jun 2025 02:27:05.606 * 100 changes in 300 seconds. Saving...

1:M 06 Jun 2025 02:27:05.606 * Background saving started by pid 66

66:C 06 Jun 2025 02:27:05.614 * DB saved on disk

66:C 06 Jun 2025 02:27:05.614 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 02:27:05.707 * Background saving terminated with success

1:M 06 Jun 2025 03:27:06.081 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 03:27:06.082 * Background saving started by pid 67

67:C 06 Jun 2025 03:27:06.090 * DB saved on disk

67:C 06 Jun 2025 03:27:06.091 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 03:27:06.182 * Background saving terminated with success

1:M 06 Jun 2025 04:27:07.057 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 04:27:07.057 * Background saving started by pid 68

68:C 06 Jun 2025 04:27:07.063 * DB saved on disk

68:C 06 Jun 2025 04:27:07.064 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 04:27:07.158 * Background saving terminated with success

1:M 06 Jun 2025 05:27:08.013 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 05:27:08.013 * Background saving started by pid 69

69:C 06 Jun 2025 05:27:08.020 * DB saved on disk

69:C 06 Jun 2025 05:27:08.020 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 05:27:08.114 * Background saving terminated with success

1:M 06 Jun 2025 06:27:09.039 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 06:27:09.039 * Background saving started by pid 70

70:C 06 Jun 2025 06:27:09.046 * DB saved on disk

70:C 06 Jun 2025 06:27:09.046 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 06:27:09.140 * Background saving terminated with success

1:M 06 Jun 2025 07:27:10.001 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 07:27:10.001 * Background saving started by pid 71

71:C 06 Jun 2025 07:27:10.007 * DB saved on disk

71:C 06 Jun 2025 07:27:10.008 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 07:27:10.102 * Background saving terminated with success

1:M 06 Jun 2025 08:27:11.012 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 08:27:11.013 * Background saving started by pid 72

72:C 06 Jun 2025 08:27:11.020 * DB saved on disk

72:C 06 Jun 2025 08:27:11.020 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 08:27:11.114 * Background saving terminated with success

1:M 06 Jun 2025 09:27:12.053 * 1 changes in 3600 seconds. Saving...

1:M 06 Jun 2025 09:27:12.054 * Background saving started by pid 73

73:C 06 Jun 2025 09:27:12.060 * DB saved on disk

73:C 06 Jun 2025 09:27:12.060 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB

1:M 06 Jun 2025 09:27:12.155 * Background saving terminated with success

## fAjiraPro (fastapi) Build Logs [fajirapro-production-6ba6.up.railway.app]

context: rkkf-9rXD

[internal] load build definition from Dockerfile

[internal] load build definition from Dockerfile

[internal] load build definition from Dockerfile  ✔ 0ms

[internal] load build definition from Dockerfile

[internal] load build definition from Dockerfile  ✔ 7ms

[internal] load metadata for docker.io/library/python:3.11-slim

[auth] library/python:pull token for registry-1.docker.io

[auth] library/python:pull token for registry-1.docker.io  ✔ 0ms

[internal] load metadata for docker.io/library/python:3.11-slim  ✔ 1s

[internal] load .dockerignore

[internal] load .dockerignore  ✔ 0ms

[internal] load .dockerignore

[internal] load .dockerignore  ✔ 7ms

[base 8/8] RUN chmod +x /app/web.sh /app/worker.sh

[base 7/8] COPY . .

[base 6/8] RUN python -m spacy download en_core_web_sm

[base 5/8] RUN pip install --no-cache-dir --upgrade pip &&     pip install --no-cache-dir -r requirements.txt

[base 4/8] COPY requirements.txt .

[internal] load build context

[base 3/8] RUN apt-get update && apt-get install -y --no-install-recommends     build-essential     curl     && apt-get clean     && rm -rf /var/lib/apt/lists/*

[base 2/8] WORKDIR /app

[base 1/8] FROM docker.io/library/python:3.11-slim@sha256:7a3ed1226224bcc1fe5443262363d42f48cf832a540c1836ba8ccbeaadf8637c

[base 1/8] FROM docker.io/library/python:3.11-slim@sha256:7a3ed1226224bcc1fe5443262363d42f48cf832a540c1836ba8ccbeaadf8637c

[internal] load build context

[internal] load build context  ✔ 0ms

[base 1/8] FROM docker.io/library/python:3.11-slim@sha256:7a3ed1226224bcc1fe5443262363d42f48cf832a540c1836ba8ccbeaadf8637c  ✔ 13ms

[internal] load build context

[internal] load build context  ✔ 12ms

[base 2/8] WORKDIR /app  ✔ 0ms – CACHED

[base 3/8] RUN apt-get update && apt-get install -y --no-install-recommends     build-essential     curl     && apt-get clean     && rm -rf /var/lib/apt/lists/*  ✔ 0ms – CACHED

[base 4/8] COPY requirements.txt .  ✔ 0ms – CACHED

[base 5/8] RUN pip install --no-cache-dir --upgrade pip &&     pip install --no-cache-dir -r requirements.txt  ✔ 0ms – CACHED

[base 6/8] RUN python -m spacy download en_core_web_sm  ✔ 0ms – CACHED

[base 7/8] COPY . .  ✔ 0ms – CACHED

[base 8/8] RUN chmod +x /app/web.sh /app/worker.sh  ✔ 0ms – CACHED

[auth] sharing credentials for production-asia-southeast1-eqsg3a.railway-registry.com

[auth] sharing credentials for production-asia-southeast1-eqsg3a.railway-registry.com  ✔ 0ms

importing to docker

importing to docker  ✔ 9s

Build time: 13.40 seconds

## fAjiraPro (fastapi) Deploy Logs [fajirapro-production-6ba6.up.railway.app]
Starting Container

INFO:app.core.config:Loaded environment variables from .env file

INFO:app.core.config:SUPABASE_URL environment variable: Set

INFO:app.core.config:SUPABASE_KEY environment variable: Set

INFO:app.core.config:REDIS_URL environment variable: Set

INFO:app.core.config:ENVIRONMENT: production

INFO:app.core.config:DEBUG: False

INFO:app.services.supabase:SUPABASE_URL: Set

INFO:app.services.supabase:SUPABASE_KEY: Set

INFO:app.services.supabase:Creating Supabase client...

INFO:app.services.supabase:Supabase client created successfully

/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:317: UserWarning: Valid config keys have changed in V2:

* 'orm_mode' has been renamed to 'from_attributes'

  warnings.warn(message, UserWarning)

WARNING:app.main:Environment CORS_ORIGINS: https://ajirapro.com,https://www.ajirapro.com,https://fajirapro.pages.dev,https://ee86f5bd.fajirapro.pages.dev

WARNING:app.core.config:CORS_ORIGINS environment variable found: https://ajirapro.com,https://www.ajirapro.com,https://fajirapro.pages.dev,https://ee86f5bd.fajirapro.pages.dev

WARNING:app.core.config:Using environment CORS origins: ['https://ajirapro.com';, 'https://www.ajirapro.com';, 'https://fajirapro.pages.dev';, 'https://ee86f5bd.fajirapro.pages.dev';]

WARNING:app.main:Settings CORS_ORIGINS: ['https://ajirapro.com';, 'https://www.ajirapro.com';, 'https://fajirapro.pages.dev';, 'https://ee86f5bd.fajirapro.pages.dev';]

WARNING:app.core.config:CORS_ORIGINS environment variable found: https://ajirapro.com,https://www.ajirapro.com,https://fajirapro.pages.dev,https://ee86f5bd.fajirapro.pages.dev

WARNING:app.core.config:Using environment CORS origins: ['https://ajirapro.com';, 'https://www.ajirapro.com';, 'https://fajirapro.pages.dev';, 'https://ee86f5bd.fajirapro.pages.dev';]

INFO:     Started server process [1]

INFO:     Waiting for application startup.

INFO:     Application startup complete.

INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)

## FajiraPro-Worker(worker) Build Logs [ajirapro-worker-production.up.railway.app]
Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.11 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.0.12)

Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.0.5)

Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.0.13)

Requirement already satisfied: cymem<2.1.0,>=2.0.2 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.0.11)

Requirement already satisfied: preshed<3.1.0,>=3.0.2 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.0.10)

Requirement already satisfied: thinc<8.3.0,>=8.1.8 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (8.2.5)

Requirement already satisfied: wasabi<1.2.0,>=0.9.1 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.1.3)

Requirement already satisfied: srsly<3.0.0,>=2.4.3 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.5.1)

Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.0.10)

Requirement already satisfied: weasel<0.4.0,>=0.1.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.3.4)

Requirement already satisfied: typer<0.10.0,>=0.3.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.9.4)

Requirement already satisfied: smart-open<7.0.0,>=5.2.1 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (6.4.0)

Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (4.67.1)

Requirement already satisfied: requests<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.32.3)

Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.4.2)

Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.1.2)

Requirement already satisfied: setuptools in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (65.5.1)

Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (23.2)

Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.5.0)

Requirement already satisfied: numpy>=1.19.0 in /usr/local/lib/python3.11/site-packages (from spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.26.4)

Requirement already satisfied: language-data>=1.2 in /usr/local/lib/python3.11/site-packages (from langcodes<4.0.0,>=3.2.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.3.0)

Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.7.0)

Requirement already satisfied: pydantic-core==2.10.1 in /usr/local/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.10.1)

Requirement already satisfied: typing-extensions>=4.6.1 in /usr/local/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (4.14.0)

Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/site-packages (from requests<3.0.0,>=2.13.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.4.2)

Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/site-packages (from requests<3.0.0,>=2.13.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.10)

Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/site-packages (from requests<3.0.0,>=2.13.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2.4.0)

Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/site-packages (from requests<3.0.0,>=2.13.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (2025.4.26)

Requirement already satisfied: blis<0.8.0,>=0.7.8 in /usr/local/lib/python3.11/site-packages (from thinc<8.3.0,>=8.1.8->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.7.11)

Requirement already satisfied: confection<1.0.0,>=0.0.1 in /usr/local/lib/python3.11/site-packages (from thinc<8.3.0,>=8.1.8->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.1.5)

Requirement already satisfied: click<9.0.0,>=7.1.1 in /usr/local/lib/python3.11/site-packages (from typer<0.10.0,>=0.3.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (8.2.1)

Requirement already satisfied: cloudpathlib<0.17.0,>=0.7.0 in /usr/local/lib/python3.11/site-packages (from weasel<0.4.0,>=0.1.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (0.16.0)

Requirement already satisfied: marisa-trie>=1.1.0 in /usr/local/lib/python3.11/site-packages (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (1.2.1)

Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/site-packages (from jinja2->spacy<3.8.0,>=3.7.2->en-core-web-sm==3.7.1) (3.0.2)

Installing collected packages: en-core-web-sm

Successfully installed en-core-web-sm-3.7.1

WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.

✔ Download and installation successful
You can now load the package via spacy.load('en_core_web_sm')

[base 6/8] RUN python -m spacy download en_core_web_sm  ✔ 5s

[base 7/8] COPY . .

[base 7/8] COPY . .  ✔ 88ms

[base 8/8] RUN chmod +x /app/web.sh /app/worker.sh

[base 8/8] RUN chmod +x /app/web.sh /app/worker.sh  ✔ 149ms

importing to docker

[auth] sharing credentials for production-asia-southeast1-eqsg3a.railway-registry.com

[auth] sharing credentials for production-asia-southeast1-eqsg3a.railway-registry.com  ✔ 0ms

importing to docker  ✔ 17s

Build time: 65.05 seconds

## FajiraPro-Worker(worker) Deploy Logs [ajirapro-worker-production.up.railway.app]
Starting Container

INFO:app.core.config:Loaded environment variables from .env file

INFO:app.core.config:SUPABASE_URL environment variable: Set

INFO:app.core.config:SUPABASE_KEY environment variable: Set

INFO:app.core.config:REDIS_URL environment variable: Set

INFO:app.core.config:ENVIRONMENT: Not set

If you wish to retain the existing behavior for retrying connections on startup,

INFO:app.core.config:DEBUG: false

you should set broker_connection_retry_on_startup to True.

INFO:app.services.supabase:SUPABASE_URL: Set

  warnings.warn(

INFO:app.services.supabase:SUPABASE_KEY: Set

 

INFO:app.services.supabase:Creating Supabase client...

INFO:app.services.supabase:Supabase client created successfully

/usr/local/lib/python3.11/site-packages/pydantic/_internal/_config.py:317: UserWarning: Valid config keys have changed in V2:

* 'orm_mode' has been renamed to 'from_attributes'

  warnings.warn(message, UserWarning)

/usr/local/lib/python3.11/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is

absolutely not recommended!

 

Please specify a different user using the --uid option.

 

User information: uid=0 euid=0 gid=0 egid=0

 

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(

 

 -------------- celery@b2122226e97d v5.3.4 (emerald-rush)

--- ***** ----- 

-- ******* ---- Linux-6.12.12+bpo-cloud-amd64-x86_64-with-glibc2.36 2025-06-06 11:18:06

- *** --- * --- 

- ** ---------- [config]

- ** ---------- .> app:         ajirapro:0x7f725818a7d0

- ** ---------- .> transport:   redis://default:**@redis-jrgh.railway.internal:6379//

- ** ---------- .> results:     redis://default:**@redis-jrgh.railway.internal:6379/

- *** --- * --- .> concurrency: 2 (prefork)

-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)

--- ***** ----- 

 -------------- [queues]

                .> celery           exchange=celery(direct) key=celery

                

 

[tasks]

  . app.tasks.resume_analysis.analyze_resume_task

  . app.tasks.resume_analysis.get_analysis_status

  . app.tasks.resume_analysis.parse_resume_task

  . app.tasks.resume_analysis.score_format_task

 

[2025-06-06 11:18:06,297: WARNING/MainProcess] /usr/local/lib/python3.11/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine

whether broker connection retries are made during startup in Celery 6.0 and above.

[2025-06-06 11:18:06,470: INFO/MainProcess] Connected to redis://default:**@redis-jrgh.railway.internal:6379//

[2025-06-06 11:18:06,471: WARNING/MainProcess] /usr/local/lib/python3.11/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine

whether broker connection retries are made during startup in Celery 6.0 and above.

If you wish to retain the existing behavior for retrying connections on startup,

you should set broker_connection_retry_on_startup to True.

  warnings.warn(

 

[2025-06-06 11:18:06,479: INFO/MainProcess] mingle: searching for neighbors

[2025-06-06 11:18:07,497: INFO/MainProcess] mingle: sync with 1 nodes

[2025-06-06 11:18:07,497: INFO/MainProcess] mingle: sync complete

[2025-06-06 11:18:07,520: INFO/MainProcess] celery@b2122226e97d ready.

