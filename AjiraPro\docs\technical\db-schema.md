Database Schema
Tables & Relationships
- profiles (User Profiles) – Stores user data linked to Supabase Auth.
- resumes (Resumes) – Tracks resumes created by users. (Index for faster user lookups)
- drafts_resume (Resume Drafts) – Saves incremental changes to unpaid resume drafts. (Index for faster lookups)
- payments (Payment Records) – Logs Flutterwave transactions. (Index for faster lookups)
- templates__resume (Resume Templates) – Stores ATS-friendly resume templates.
- job_descriptions (Saved Job Descriptions) – Optional: Job descriptions used for tailoring resumes. (Index for faster resume lookups)
## Relationships
• profiles → resumes: 1-to-many (one user → many resumes)
• resumes → drafts: 1-to-many (one resume → many drafts)
• resumes → templates: Many-to-1 (many resumes → one template)
• resumes → payments: 1-to-1 (one resume → one payment)
• resumes → job_descriptions: 1-to-1 (one resume → one job description)
Row-Level Security Policies
Create and apply All necessary policies
drafts, payments, job_descriptions Tables
Create and apply RLS policies to ensure users can only access their own data.
Examples of SQL Functions & Triggers
Auto-Delete Expired Drafts
Update Resume Status on Payment
Create User Profile on Auth
Key Database Workflows
User Creates a Resume:
1.	Insert into resumes table with status 'pending_payment'
2.	Save initial draft in drafts_resume table
3.	Generate payment link with resume_id
User Pays for Resume:
1.	Payment webhook updates payments table (status = 'paid')
2.	Trigger updates resume status to 'processing'
3.	Backend services get notified to start generation
AI Generates Resume:
1.	Backend processes the resume generation
2.	File is uploaded to Supabase Storage
3.	Update resumes table with file_path and status = 'completed'
4.	Update ats_score with ATS compliance rating

*NOTE*: ALL THESE CAN BE EDITED/UPDATED AS NEEDED, AS DEVELOPMENT PROGRESSES
