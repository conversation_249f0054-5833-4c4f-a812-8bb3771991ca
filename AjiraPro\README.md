# AjiraPro

AjiraPro is a web-based platform designed to help job seekers create, enhance, and tailor professional resumes and CVs specifically for the Kenyan job market.

## Features

- AI-powered CV and resume tailoring based on job descriptions
- ATS compliance checker with improvement suggestions
- Step-by-step CV and resume builder with AI guidance
- CV revamp tool for modernizing existing documents
- Multiple ATS-friendly templates
- Cover letter generator based on job descriptions

## Tech Stack

- **Frontend**: React (TypeScript), Tailwind CSS, Framer Motion
- **Backend**: FastAPI (Python), Celery, Redis
- **Database**: Supabase PostgreSQL
- **Auth**: Supabase Auth
- **Storage**: Supabase Storage
- **AI/ML**: OpenAI GPT, spaCy, LangChain
- **Payments**: Flutterwave

## Project Structure

```
AjiraPro/
├── frontend/                   # React (TypeScript)
│   ├── public/                 # Static assets
│   └── src/
│       ├── components/         # Reusable UI components
│       ├── hooks/              # Custom hooks
│       ├── pages/              # Page components
│       ├── types/              # TypeScript interfaces
│       └── utils/              # Helper functions
│
├── backend/                    # FastAPI (Python)
│   ├── app/
│   │   ├── routers/            # API endpoints
│   │   ├── services/           # Business logic
│   │   ├── models/             # Pydantic schemas
│   │   ├── core/               # Config and middleware
│   │   └── templates/          # Word templates
│   └── tests/                  # Integration/unit tests
│
└── docs/                       # Documentation
    ├── requirements/           # Project requirements
    ├── technical/              # Technical documentation
    ├── guidelines/             # Development guidelines
    └── deployment/             # Deployment instructions
```

## Getting Started

### Prerequisites

- Node.js (v16+)
- Python (v3.9+)
- Redis
- Supabase account
- OpenAI API key
- Flutterwave account

### Frontend Setup

```bash
cd frontend
npm install
npm start
```

### Backend Setup

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

## Documentation

See the `/docs` directory for detailed documentation on requirements, architecture, and development guidelines.

## Deployment

The application is deployed on:

- **Backend**: Railway (AjiraProMax project)
- **Frontend**: Cloudflare Pages

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions and troubleshooting.

## License

[MIT License](LICENSE)
