<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Resume Generator</title>
    <style>
        /* General Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        h1, h2, h3 {
            color: #2c3e50;
        }

        /* UI Section Styles */
        .ui-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .ui-header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ui-title {
            font-size: 24px;
        }

        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #6c757d;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .instructions code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: monospace;
        }

        .json-input {
            width: 100%;
            height: 200px;
            padding: 10px;
            font-family: monospace;
            border: 1px solid #ced4da;
            border-radius: 4px;
            resize: vertical;
            margin-bottom: 15px;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .btn-success {
            background-color: #2ecc71;
            color: white;
        }

        .btn-success:hover {
            background-color: #27ae60;
        }

        /* Error Message Styles */
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        /* Loading Indicator */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Preview Section */
        .preview-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .preview-title {
            font-size: 24px;
        }

        /* Resume Styles */
        .resume-container {
            max-width: 8.5in;
            margin: 0 auto;
            padding: 0.5in;
            border: 1px solid #ccc;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
        }

        /* Personal information section */
        .personal-header {
            margin-bottom: 20px;
        }

        .name {
            font-size: 18pt;
            font-weight: bold;
            color: #006400; /* Dark green color from sample */
            text-align: center;
            margin-bottom: 5px;
        }

        .contact-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            font-size: 10pt;
        }

        .website {
            color: #006400;
        }

        /* Section styles */
        .resume-section {
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 14pt;
            font-weight: bold;
            color: #006400; /* Dark green color from sample */
            text-transform: uppercase;
            margin-bottom: 5px;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
        }

        /* Experience and Education entries */
        .entry {
            margin-bottom: 10px;
        }

        .entry-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }

        .entry-title {
            font-weight: bold;
        }

        .entry-location {
            text-align: right;
        }

        .entry-subtitle {
            font-style: italic;
            margin-bottom: 2px;
        }

        /* Bullet points */
        .bullet-list {
            padding-left: 20px;
            margin-bottom: 5px;
        }

        .sub-bullet-list {
            padding-left: 20px;
            list-style-type: circle;
        }

        /* Links */
        .resume-container a {
            color: #000;
            text-decoration: none;
        }

        .resume-container a:hover {
            text-decoration: underline;
        }

        /* Skills section */
        .skills-list {
            display: flex;
            flex-wrap: wrap;
        }

        .skill-category {
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .skill-category-title {
            font-weight: bold;
        }

        /* Documentation section */
        #documentation {
            margin-top: 20px;
            display: none;
        }

        /* Print styles for PDF export */
        @media print {
            body {
                padding: 0;
                margin: 0;
                background-color: white;
            }

            .container, .ui-section, .preview-section, .preview-header, .button-group {
                display: none !important;
            }

            .resume-container {
                display: block !important;
                border: none;
                padding: 0.25in;
                width: 100%;
                box-shadow: none;
                max-width: none;
            }

            /* Pagination controls */
            .resume-section {
                page-break-inside: avoid;
            }

            .section-title {
                page-break-after: avoid;
            }

            .entry {
                page-break-inside: avoid;
            }

            /* Ensure proper margins */
            @page {
                margin: 0.5in;
            }
        }

        /* Responsive design */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .resume-container {
                padding: 0.25in;
            }

            .entry-header {
                flex-direction: column;
            }

            .entry-location {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- UI Section -->
        <section class="ui-section">
            <div class="ui-header">
                <h1 class="ui-title">Dynamic Resume Generator</h1>
            </div>

            <div class="instructions">
                <h3>Instructions:</h3>
                <p>Paste your resume data in JSON format into the text area below. The generator will create a professionally formatted resume based on your data.</p>
                <p>The generator is flexible and can handle different JSON structures. Click "Load Sample Data" to see an example format.</p>
                <p>After generating your resume, you can print or save it as a PDF using the "Print/Save as PDF" button.</p>
            </div>

            <div class="error-message" id="error-message"></div>

            <textarea id="json-input" class="json-input" placeholder="Paste your resume JSON data here..."></textarea>

            <div class="button-group">
                <button id="generate-btn" class="btn btn-primary">Generate Resume</button>
                <button id="sample-btn" class="btn btn-secondary">Load Sample Data</button>
                <button id="clear-btn" class="btn btn-secondary">Clear</button>
            </div>
        </section>

        <!-- Loading Indicator -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Generating your resume...</p>
        </div>

        <!-- Preview Section -->
        <section id="preview-section" class="preview-section">
            <div class="preview-header">
                <h2 class="preview-title">Resume Preview</h2>
                <div class="button-group">
                    <button id="print-btn" class="btn btn-success">Print/Save as PDF</button>
                </div>
            </div>

            <div id="resume-container" class="resume-container">
                <!-- Resume content will be dynamically inserted here -->
            </div>
        </section>
    </div>

    <!-- Documentation Section -->
    <div class="container" id="documentation">
        <section class="ui-section">
            <div class="ui-header">
                <h1 class="ui-title">Resume Generator Documentation</h1>
                <button id="close-docs-btn" class="btn btn-secondary">Close</button>
            </div>

            <div class="instructions">
                <h3>JSON Structure</h3>
                <p>The resume generator accepts various JSON formats. Here's the recommended structure:</p>
                <pre style="background-color: #f8f9fa; padding: 10px; overflow-x: auto;">
{
  "name": "Your Full Name",
  "contact_information": {
    "email": "<EMAIL>",
    "mobile": "******-567-8900"
  },
  "website": "https://yourwebsite.com",
  "education": [
    {
      "institution": "University Name",
      "location": "City, Country",
      "degree": "Degree Name",
      "field_of_study": "Your Major",
      "gpa": "3.8/4.0",
      "dates": "Start Date - End Date"
    }
  ],
  "experience": [
    {
      "company": "Company Name",
      "location": "City, Country",
      "role": "Your Job Title",
      "dates": "Start Date - End Date",
      "responsibilities": "Description of your role and achievements. Each sentence will be converted to a bullet point."
    }
  ],
  "side_projects": [
    {
      "project_name": "Project Name",
      "description": "Description of your project"
    }
  ],
  "programming_skills": "List of programming languages",
  "technologies": "List of technologies"
}
                </pre>

                <h3>Adding Custom Sections</h3>
                <p>To add custom sections, include them in your JSON with appropriate naming. The generator will automatically create sections based on the data structure.</p>

                <h3>Formatting Tips</h3>
                <ul>
                    <li>For experience responsibilities, separate different points with newlines (\n) or complete sentences ending with periods.</li>
                    <li>Dates can be in any format, but "Month Year - Month Year" is recommended for consistency.</li>
                    <li>For PDF export, use the Print/Save as PDF button and select "Save as PDF" in your browser's print dialog.</li>
                </ul>

                <h3>Troubleshooting</h3>
                <p>If your resume doesn't render correctly:</p>
                <ul>
                    <li>Check that your JSON is valid (no missing commas, brackets, etc.)</li>
                    <li>Ensure required fields like name are included</li>
                    <li>For complex formatting in descriptions, use plain text with minimal formatting</li>
                </ul>
            </div>

    <script>
        // Sample resume data
        const sampleResumeData = {
            "name": "Pragyanandesh Narayan Tripathi",
            "contact_information": {
                "email": "<EMAIL>",
                "mobile": "+91-************"
            },
            "website": "https://www.nerdsden.com",
            "education": [
                {
                    "institution": "Indian Institute of Technology",
                    "location": "Kanpur, India",
                    "degree": "Master of Engineering",
                    "field_of_study": "Electrical Engineering",
                    "gpa": "8.5/10",
                    "dates": "July, 2013 - June, 2014"
                },
                {
                    "institution": "Indian Institute of Technology",
                    "location": "Kanpur, India",
                    "degree": "Bachelor of Engineering",
                    "field_of_study": "Electrical Engineering",
                    "gpa": "6.7/10",
                    "dates": "July, 2009 - June, 2013"
                }
            ],
            "experience": [
                {
                    "company": "Circle Internet Pvt. Ltd.",
                    "location": "Bangalore, India",
                    "role": "Engineering Lead",
                    "dates": "August 2018 - Present",
                    "responsibilities": "Analytics Pipeline: Currently I am working on setting up a analytics pipeline which could recommend users content on the basis of their preferences. Been improving our overall engagement\nDistributed Event Logging: Setting up ELK cluster over Kubernetes. Wrote a composable logging method and directed logs to ELK cluster and BigQuery and InfluxDB\nKubernetes Infrastructure Setup: Successfully deployed stateful MongoDB services, and Horizonted server application over kubernetes\nBackend Server: Wrote initial version of the backend server. It involved resuming media uploads, content management system, profile management, etc. You can find the passworded version of project structure at this link"
                },
                {
                    "company": "Elanic Services",
                    "location": "Bangalore, India",
                    "role": "Backend Team Lead",
                    "dates": "August 2015 - November 2018",
                    "responsibilities": "Engineering Team Manager: Managed team of 8 people, helped them to design flexible architecture. I was also responsible for on-boarding new employees and make them comfortable with the existing backend system.\nFeed Service Based On Click Engagement: Setup logging using ELK cluster. Wrote a series of jobs to calculate click's engagement for products. It helped us improve our overall conversion from 3 to 15 percent.\nACID Transactions in MongoDB: We were using MongoDB-3.6. Wrote a wrapper library to support ACID transactions using 2-Phase Commit.\nLogistics Optimization: Wrote an engine to dynamically choose logistics partners to pick-up and deliver the products and optimize for cost and speed based on the time given by the operations team.\nFeed Engine: Implemented the backend for dynamically generating product feeds. Integrated rules engine which enabled the marketing team to generate different feeds for different user segments, hence enabling feed experimentation.\nBusiness Rules Engine: Wrote in-house business rules engine to dynamically calculate sales commission, pickup charges, delivery charges, voucher discounts, etc.\nBackend Server: Wrote the backend server for Elanic in NodeJS. It involved profile management system, Product listing, Order management, Logistics Management"
                },
                {
                    "company": "Samsung Research",
                    "location": "Bangalore, India",
                    "role": "Senior Software Engineer",
                    "dates": "July 2014 - June 2015",
                    "responsibilities": "Coded Rolling Shutter: The idea was to generate higher resolution images using existing sensors in Samsung devices. Coded rolling shutter is an algorithm to do that. Tried implementing it using rafiki library\nVirtual Tour Application: Worked on synthetic range map generation to enable a user to enjoy three-dimensional virtual tours of places from the series of images."
                }
            ],
            "side_projects": [
                {
                    "project_name": "Vadelabs",
                    "description": "Making business development easier. Building services which can be used across business domains like Business Rules Engine, Feed Generation Engine, Notification Manager, etc."
                },
                {
                    "project_name": "Nerdsden",
                    "description": "Memoirs of a skeptic nerd. A place to share my learnings with fellow developers. Wrote the current live version using Gatsby."
                },
                {
                    "project_name": "Cradle",
                    "description": "I am currently working on writing a Simulation Testing Tool For REST APIs"
                }
            ],
            "programming_skills": "Javascript, Python, Clojure, C++, SQL",
            "technologies": "AWS, React, Kafka, MongoDB, Elasticsearch, Redis, NodeJS, Kubernetes"
        };

        // DOM Elements
        const jsonInput = document.getElementById('json-input');
        const generateBtn = document.getElementById('generate-btn');
        const sampleBtn = document.getElementById('sample-btn');
        const clearBtn = document.getElementById('clear-btn');
        const printBtn = document.getElementById('print-btn');
        const errorMessage = document.getElementById('error-message');
        const loading = document.getElementById('loading');
        const previewSection = document.getElementById('preview-section');
        const resumeContainer = document.getElementById('resume-container');

        // DOM Elements for documentation
        const documentationSection = document.getElementById('documentation');
        const closeDocsBtn = document.getElementById('close-docs-btn');
        const showDocsBtn = document.createElement('button');
        showDocsBtn.id = 'show-docs-btn';
        showDocsBtn.className = 'btn btn-secondary';
        showDocsBtn.textContent = 'Show Documentation';

        // Event Listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Add documentation button to UI
            const buttonGroup = document.querySelector('.ui-section .button-group');
            buttonGroup.appendChild(showDocsBtn);

            // Set up event listeners
            generateBtn.addEventListener('click', generateResume);
            sampleBtn.addEventListener('click', loadSampleData);
            clearBtn.addEventListener('click', clearForm);
            printBtn.addEventListener('click', printResume);
            showDocsBtn.addEventListener('click', showDocumentation);
            closeDocsBtn.addEventListener('click', hideDocumentation);
        });

        // Show documentation
        function showDocumentation() {
            documentationSection.style.display = 'block';
            documentationSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Hide documentation
        function hideDocumentation() {
            documentationSection.style.display = 'none';
        }

        // Load sample data
        function loadSampleData() {
            jsonInput.value = JSON.stringify(sampleResumeData, null, 2);
            hideError();
        }

        // Clear the form
        function clearForm() {
            jsonInput.value = '';
            hideError();
            previewSection.style.display = 'none';
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        // Hide error message
        function hideError() {
            errorMessage.style.display = 'none';
        }

        // Print or save as PDF
        function printResume() {
            window.print();
        }

        // Generate resume from JSON data
        function generateResume() {
            hideError();

            // Get JSON data from textarea
            const jsonData = jsonInput.value.trim();

            if (!jsonData) {
                showError('Please enter your resume data in JSON format.');
                return;
            }

            // Parse JSON data
            let resumeData;
            try {
                resumeData = JSON.parse(jsonData);
            } catch (error) {
                showError(`Invalid JSON format: ${error.message}`);
                return;
            }

            // Validate resume data
            if (!validateResumeData(resumeData)) {
                return;
            }

            // Show loading indicator
            loading.style.display = 'block';

            // Use setTimeout to allow the UI to update before processing
            setTimeout(() => {
                try {
                    // Render the resume
                    renderResume(resumeData);

                    // Hide loading indicator and show preview
                    loading.style.display = 'none';
                    previewSection.style.display = 'block';

                    // Scroll to preview
                    previewSection.scrollIntoView({ behavior: 'smooth' });
                } catch (error) {
                    loading.style.display = 'none';
                    showError(`Error generating resume: ${error.message}`);
                }
            }, 100);
        }

        // Validate resume data
        function validateResumeData(data) {
            // Check if data is an object
            if (!data || typeof data !== 'object') {
                showError('Resume data must be a valid JSON object.');
                return false;
            }

            // Check for name (required)
            if (!data.name && !data.personalInfo?.name) {
                showError('Resume data must include a name field.');
                return false;
            }

            return true;
        }

        // Normalize resume data to a standard format
        function normalizeResumeData(data) {
            const normalized = {
                personalInfo: {},
                education: [],
                experience: [],
                skills: [],
                projects: [],
                internships: [],
                certifications: [],
                achievements: [],
                customSections: {}
            };

            // Personal Info
            normalized.personalInfo.name = data.name || data.personalInfo?.name || '';
            normalized.personalInfo.title = data.title || data.job_title || '';

            // Handle different contact information formats
            if (data.contact) {
                normalized.personalInfo.email = data.contact.email || '';
                normalized.personalInfo.phone = data.contact.phone || data.contact.mobile || '';
                normalized.personalInfo.address = data.contact.address || '';
            } else if (data.contact_information) {
                normalized.personalInfo.email = data.contact_information.email || '';
                normalized.personalInfo.phone = data.contact_information.mobile || data.contact_information.phone || '';
                normalized.personalInfo.address = data.contact_information.address || '';
            } else {
                normalized.personalInfo.email = data.email || data.personalInfo?.email || '';
                normalized.personalInfo.phone = data.phone || data.contact_number || data.mobile || data.personalInfo?.phone || data.personalInfo?.mobile || '';
                normalized.personalInfo.address = data.address || data.personalInfo?.address || '';
            }

            normalized.personalInfo.website = data.website || data.personalInfo?.website || '';

            // Career Objective / Summary
            if (data.career_objective || data.personal_profile || data.summary || data.objective) {
                normalized.customSections.profile = {
                    title: 'Career Objective',
                    content: data.career_objective || data.personal_profile || data.summary || data.objective
                };
            }

            // Education - check for different field names
            const educationData = data.education || data.educational_background || data.education_history || [];
            if (Array.isArray(educationData)) {
                normalized.education = educationData.map(edu => ({
                    institution: edu.institution || edu.school || edu.university || '',
                    location: edu.location || '',
                    degree: edu.degree || edu.qualification || '',
                    fieldOfStudy: edu.field_of_study || edu.fieldOfStudy || '',
                    gpa: edu.gpa || '',
                    details: edu.details || '',
                    startDate: edu.startDate || (edu.dates ? edu.dates.split(' - ')[0] : '') ||
                              (edu.dates_attended ? edu.dates_attended.split('-')[0] : '') ||
                              (edu.duration ? edu.duration.split(',')[1]?.trim() : ''),
                    endDate: edu.endDate || (edu.dates ? edu.dates.split(' - ')[1] : '') ||
                            (edu.dates_attended ? edu.dates_attended.split('-')[1] : '') ||
                            (edu.duration ? edu.duration.split(',')[0]?.trim() : '')
                }));
            }

            // Experience - check for different field names
            const experienceData = data.experience || data.working_experiences || data.work_history || data.employment_history || [];
            if (Array.isArray(experienceData)) {
                normalized.experience = experienceData.map(exp => ({
                    company: exp.company || exp.organization || exp.employer || '',
                    location: exp.location || '',
                    title: exp.title || exp.role || exp.position || '',
                    startDate: exp.startDate || (exp.dates ? exp.dates.split(' - ')[0] : '') ||
                              (exp.dates_employed ? exp.dates_employed.split(' - ')[0] : ''),
                    endDate: exp.endDate || (exp.dates ? exp.dates.split(' - ')[1] : '') ||
                            (exp.dates_employed ? exp.dates_employed.split(' - ')[1] : ''),
                    bullets: exp.bullets || (exp.responsibilities ? parseResponsibilities(exp.responsibilities) : [])
                }));
            }

            // Internships
            if (Array.isArray(data.internships)) {
                normalized.internships = data.internships.map(intern => ({
                    organization: intern.organization || intern.company || '',
                    role: intern.role || intern.position || intern.title || '',
                    duration: intern.duration || '',
                    responsibilities: intern.responsibilities || ''
                }));
            }

            // Skills
            if (Array.isArray(data.skills)) {
                // Check if skills is an array of objects with a 'skill' property
                if (data.skills.length > 0 && typeof data.skills[0] === 'object' && data.skills[0].skill) {
                    normalized.skills = data.skills.map(s => s.skill);
                } else {
                    normalized.skills = data.skills;
                }
            } else if (data.skills && typeof data.skills === 'object') {
                normalized.skills = data.skills;
            } else if (data.programming_skills || data.technologies) {
                normalized.skills = [
                    ...(data.programming_skills ? [data.programming_skills] : []),
                    ...(data.technologies ? [data.technologies] : [])
                ];
            } else if (data.eligibility) {
                normalized.skills = [data.eligibility];
            }

            // Projects
            if (Array.isArray(data.projects)) {
                normalized.projects = data.projects.map(proj => ({
                    name: proj.title || proj.project_name || proj.name || '',
                    role: proj.role || '',
                    description: proj.description || ''
                }));
            } else if (Array.isArray(data.side_projects)) {
                normalized.projects = data.side_projects.map(proj => ({
                    name: proj.project_name || proj.name || '',
                    description: proj.description || ''
                }));
            }

            // Certifications
            if (Array.isArray(data.certifications)) {
                // Check if certifications is an array of objects with a 'certification' property
                if (data.certifications.length > 0 && typeof data.certifications[0] === 'object' && data.certifications[0].certification) {
                    normalized.certifications = data.certifications.map(c => c.certification);
                } else {
                    normalized.certifications = data.certifications;
                }
            }

            // Achievements
            if (Array.isArray(data.achievements)) {
                // Check if achievements is an array of objects with an 'achievement' property
                if (data.achievements.length > 0 && typeof data.achievements[0] === 'object' && data.achievements[0].achievement) {
                    normalized.achievements = data.achievements.map(a => a.achievement);
                } else {
                    normalized.achievements = data.achievements;
                }
            }

            // Process custom sections
            // Personal Information
            if (data.personal_information) {
                normalized.customSections.personalDetails = {
                    title: 'Personal Information',
                    items: []
                };

                for (const [key, value] of Object.entries(data.personal_information)) {
                    normalized.customSections.personalDetails.items.push({
                        label: formatLabel(key),
                        value: value
                    });
                }
            }

            // Character References
            if (Array.isArray(data.character_references)) {
                normalized.customSections.references = {
                    title: 'Character References',
                    items: data.character_references.map(ref => ({
                        name: ref.name,
                        position: ref.position,
                        contact: ref.contact_number
                    }))
                };
            }

            // Seminars/Training
            if (Array.isArray(data.seminars_training_attended)) {
                normalized.customSections.seminars = {
                    title: 'Seminars & Training',
                    items: data.seminars_training_attended.map(seminar => ({
                        title: seminar.title,
                        location: seminar.location,
                        dates: seminar.dates
                    }))
                };
            }

            // Hobbies and Interests
            if (data.hobbies_and_interests) {
                normalized.customSections.hobbies = {
                    title: 'Hobbies & Interests',
                    content: data.hobbies_and_interests
                };
            }

            return normalized;
        }

        // Helper function to format label from snake_case to Title Case
        function formatLabel(key) {
            return key
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        }

        // Parse responsibilities text into bullet points
        function parseResponsibilities(responsibilitiesText) {
            if (!responsibilitiesText) return [];

            // Split by newlines or periods followed by capital letters
            return responsibilitiesText
                .split(/\n|(?<=\.)\s*(?=[A-Z])/)
                .map(item => item.trim())
                .filter(item => item.length > 0);
        }

        // Render the resume
        function renderResume(data) {
            // Clear the resume container
            resumeContainer.innerHTML = '';

            // Normalize the data to a standard format
            const normalizedData = normalizeResumeData(data);

            // Render personal information
            renderPersonalInfo(normalizedData.personalInfo);

            // Render profile/summary section if available
            if (normalizedData.customSections.profile) {
                renderProfileSection(normalizedData.customSections.profile);
            }

            // Render personal details section if available
            if (normalizedData.customSections.personalDetails) {
                renderPersonalDetailsSection(normalizedData.customSections.personalDetails);
            }

            // Render education section
            if (normalizedData.education.length > 0) {
                renderEducationSection(normalizedData.education);
            }

            // Render experience section
            if (normalizedData.experience.length > 0) {
                renderExperienceSection(normalizedData.experience);
            }

            // Render internships section
            if (normalizedData.internships.length > 0) {
                renderInternshipsSection(normalizedData.internships);
            }

            // Render skills section
            if (normalizedData.skills.length > 0) {
                renderSkillsListSection(normalizedData.skills);
            } else if (typeof normalizedData.skills === 'object' && Object.keys(normalizedData.skills).length > 0) {
                renderSkillsSection(normalizedData.skills);
            }

            // Render projects section
            if (normalizedData.projects.length > 0) {
                renderProjectsSection(normalizedData.projects);
            }

            // Render certifications section
            if (normalizedData.certifications.length > 0) {
                renderCertificationsSection(normalizedData.certifications);
            }

            // Render achievements section
            if (normalizedData.achievements.length > 0) {
                renderAchievementsSection(normalizedData.achievements);
            }

            // Render seminars section if available
            if (normalizedData.customSections.seminars) {
                renderSeminarsSection(normalizedData.customSections.seminars);
            }

            // Render hobbies section if available
            if (normalizedData.customSections.hobbies) {
                renderHobbiesSection(normalizedData.customSections.hobbies);
            }

            // Render references section if available
            if (normalizedData.customSections.references) {
                renderReferencesSection(normalizedData.customSections.references);
            }
        }

        // Render internships section
        function renderInternshipsSection(internshipsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'INTERNSHIPS';
            sectionDiv.appendChild(titleElement);

            const internshipsList = document.createElement('ul');
            internshipsList.className = 'bullet-list';

            internshipsData.forEach(internship => {
                const listItem = document.createElement('li');

                // Organization and role
                const headerDiv = document.createElement('div');
                headerDiv.className = 'entry-header';

                const organizationElement = document.createElement('span');
                organizationElement.className = 'entry-title';
                organizationElement.textContent = internship.organization;
                headerDiv.appendChild(organizationElement);

                listItem.appendChild(headerDiv);

                // Role
                const roleElement = document.createElement('div');
                roleElement.className = 'entry-subtitle';
                roleElement.textContent = internship.role;
                listItem.appendChild(roleElement);

                // Duration
                if (internship.duration) {
                    const durationElement = document.createElement('div');
                    durationElement.className = 'entry-subtitle';
                    durationElement.style.fontStyle = 'italic';
                    durationElement.textContent = internship.duration;
                    listItem.appendChild(durationElement);
                }

                // Responsibilities
                if (internship.responsibilities) {
                    const responsibilitiesElement = document.createElement('div');
                    responsibilitiesElement.textContent = internship.responsibilities;
                    listItem.appendChild(responsibilitiesElement);
                }

                internshipsList.appendChild(listItem);
            });

            sectionDiv.appendChild(internshipsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render skills as a list
        function renderSkillsListSection(skillsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'SKILLS';
            sectionDiv.appendChild(titleElement);

            // Create skills list
            const skillsList = document.createElement('ul');
            skillsList.className = 'bullet-list';

            skillsData.forEach(skill => {
                const listItem = document.createElement('li');
                listItem.textContent = skill;
                skillsList.appendChild(listItem);
            });

            sectionDiv.appendChild(skillsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render certifications section
        function renderCertificationsSection(certificationsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'CERTIFICATIONS';
            sectionDiv.appendChild(titleElement);

            // Create certifications list
            const certificationsList = document.createElement('ul');
            certificationsList.className = 'bullet-list';

            certificationsData.forEach(certification => {
                const listItem = document.createElement('li');
                listItem.textContent = certification;
                certificationsList.appendChild(listItem);
            });

            sectionDiv.appendChild(certificationsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render achievements section
        function renderAchievementsSection(achievementsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'ACHIEVEMENTS';
            sectionDiv.appendChild(titleElement);

            // Create achievements list
            const achievementsList = document.createElement('ul');
            achievementsList.className = 'bullet-list';

            achievementsData.forEach(achievement => {
                const listItem = document.createElement('li');
                listItem.textContent = achievement;
                achievementsList.appendChild(listItem);
            });

            sectionDiv.appendChild(achievementsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render profile/summary section
        function renderProfileSection(profileData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = profileData.title;
            sectionDiv.appendChild(titleElement);

            // Create content
            const contentDiv = document.createElement('div');
            contentDiv.textContent = profileData.content;
            sectionDiv.appendChild(contentDiv);

            resumeContainer.appendChild(sectionDiv);
        }

        // Render personal details section
        function renderPersonalDetailsSection(detailsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = detailsData.title;
            sectionDiv.appendChild(titleElement);

            // Create details list
            const detailsList = document.createElement('ul');
            detailsList.style.listStyleType = 'none';
            detailsList.style.paddingLeft = '0';

            detailsData.items.forEach(item => {
                const listItem = document.createElement('li');
                listItem.innerHTML = `<strong>${item.label}:</strong> ${item.value}`;
                detailsList.appendChild(listItem);
            });

            sectionDiv.appendChild(detailsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render seminars section
        function renderSeminarsSection(seminarsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = seminarsData.title;
            sectionDiv.appendChild(titleElement);

            // Create seminars list
            const seminarsList = document.createElement('ul');
            seminarsList.className = 'bullet-list';

            seminarsData.items.forEach(seminar => {
                const listItem = document.createElement('li');

                // Seminar title
                const titleElement = document.createElement('div');
                titleElement.className = 'entry-title';
                titleElement.textContent = seminar.title;
                listItem.appendChild(titleElement);

                // Location
                if (seminar.location) {
                    const locationElement = document.createElement('div');
                    locationElement.className = 'entry-subtitle';
                    locationElement.textContent = seminar.location;
                    listItem.appendChild(locationElement);
                }

                // Dates
                if (seminar.dates) {
                    const datesElement = document.createElement('div');
                    datesElement.className = 'entry-subtitle';
                    datesElement.style.textAlign = 'right';
                    datesElement.textContent = seminar.dates;
                    listItem.appendChild(datesElement);
                }

                seminarsList.appendChild(listItem);
            });

            sectionDiv.appendChild(seminarsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render hobbies section
        function renderHobbiesSection(hobbiesData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = hobbiesData.title;
            sectionDiv.appendChild(titleElement);

            // Create content
            const contentDiv = document.createElement('div');
            contentDiv.textContent = hobbiesData.content;
            sectionDiv.appendChild(contentDiv);

            resumeContainer.appendChild(sectionDiv);
        }

        // Render references section
        function renderReferencesSection(referencesData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = referencesData.title;
            sectionDiv.appendChild(titleElement);

            // Create references list
            const referencesList = document.createElement('ul');
            referencesList.className = 'bullet-list';

            referencesData.items.forEach(reference => {
                const listItem = document.createElement('li');

                // Reference name
                const nameElement = document.createElement('div');
                nameElement.className = 'entry-title';
                nameElement.textContent = reference.name;
                listItem.appendChild(nameElement);

                // Position
                if (reference.position) {
                    const positionElement = document.createElement('div');
                    positionElement.className = 'entry-subtitle';
                    positionElement.textContent = reference.position;
                    listItem.appendChild(positionElement);
                }

                // Contact
                if (reference.contact) {
                    const contactElement = document.createElement('div');
                    contactElement.textContent = `Contact: ${reference.contact}`;
                    listItem.appendChild(contactElement);
                }

                referencesList.appendChild(listItem);
            });

            sectionDiv.appendChild(referencesList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render personal information section
        function renderPersonalInfo(personalInfo) {
            const headerDiv = document.createElement('div');
            headerDiv.className = 'personal-header';

            // Name
            const nameElement = document.createElement('h1');
            nameElement.className = 'name';
            nameElement.textContent = personalInfo.name;
            headerDiv.appendChild(nameElement);

            // Title/Job Title (if available)
            if (personalInfo.title) {
                const titleDiv = document.createElement('div');
                titleDiv.style.textAlign = 'center';
                titleDiv.style.fontSize = '14pt';
                titleDiv.style.fontWeight = 'bold';
                titleDiv.style.color = '#444';
                titleDiv.style.marginBottom = '10px';
                titleDiv.textContent = personalInfo.title;
                headerDiv.appendChild(titleDiv);
            }

            // Contact information
            const contactDiv = document.createElement('div');
            contactDiv.className = 'contact-info';

            // Left side - website/portfolio
            if (personalInfo.website) {
                const websiteDiv = document.createElement('div');
                websiteDiv.className = 'website';
                const websiteLink = document.createElement('a');
                websiteLink.href = personalInfo.website;
                websiteLink.textContent = personalInfo.website;
                websiteDiv.appendChild(websiteLink);
                contactDiv.appendChild(websiteDiv);
            }

            // Right side - email, phone, address
            const contactDetailsDiv = document.createElement('div');
            contactDetailsDiv.className = 'contact-details';

            if (personalInfo.email) {
                const emailSpan = document.createElement('span');
                emailSpan.innerHTML = `Email : <a href="mailto:${personalInfo.email}">${personalInfo.email}</a>`;
                contactDetailsDiv.appendChild(emailSpan);
            }

            if (personalInfo.phone) {
                const phoneSpan = document.createElement('span');
                phoneSpan.innerHTML = personalInfo.email ? ` | Mobile : ${personalInfo.phone}` : `Mobile : ${personalInfo.phone}`;
                contactDetailsDiv.appendChild(phoneSpan);
            }

            contactDiv.appendChild(contactDetailsDiv);
            headerDiv.appendChild(contactDiv);

            // Address (if available)
            if (personalInfo.address) {
                const addressDiv = document.createElement('div');
                addressDiv.className = 'address';
                addressDiv.style.textAlign = 'center';
                addressDiv.style.marginTop = '5px';
                addressDiv.style.fontSize = '10pt';
                addressDiv.textContent = personalInfo.address;
                headerDiv.appendChild(addressDiv);
            }

            resumeContainer.appendChild(headerDiv);
        }

        // Render education section
        function renderEducationSection(educationData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'EDUCATION';
            sectionDiv.appendChild(titleElement);

            const educationList = document.createElement('ul');
            educationList.className = 'bullet-list';

            educationData.forEach(edu => {
                const listItem = document.createElement('li');

                // Institution and location
                const headerDiv = document.createElement('div');
                headerDiv.className = 'entry-header';

                const institutionElement = document.createElement('span');
                institutionElement.className = 'entry-title';
                institutionElement.textContent = edu.institution;
                headerDiv.appendChild(institutionElement);

                const locationElement = document.createElement('span');
                locationElement.className = 'entry-location';
                locationElement.textContent = edu.location || '';
                headerDiv.appendChild(locationElement);

                listItem.appendChild(headerDiv);

                // Degree and GPA
                const degreeElement = document.createElement('div');
                degreeElement.className = 'entry-subtitle';
                let degreeText = edu.degree;
                if (edu.fieldOfStudy) {
                    degreeText += ` in ${edu.fieldOfStudy}`;
                }
                if (edu.gpa) {
                    degreeText += `; GPA: ${edu.gpa}`;
                }
                degreeElement.textContent = degreeText;
                listItem.appendChild(degreeElement);

                // Date range
                if (edu.startDate || edu.endDate) {
                    const dateElement = document.createElement('div');
                    dateElement.className = 'entry-subtitle';
                    dateElement.style.textAlign = 'right';
                    dateElement.textContent = `${edu.startDate || ''} - ${edu.endDate || ''}`;
                    listItem.appendChild(dateElement);
                }

                // Additional details
                if (edu.details && edu.details.trim()) {
                    const detailsElement = document.createElement('div');
                    detailsElement.style.fontSize = '10pt';
                    detailsElement.style.fontStyle = 'italic';
                    detailsElement.style.marginTop = '3px';
                    detailsElement.textContent = edu.details;
                    listItem.appendChild(detailsElement);
                }

                educationList.appendChild(listItem);
            });

            sectionDiv.appendChild(educationList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render experience section
        function renderExperienceSection(experienceData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'EXPERIENCE';
            sectionDiv.appendChild(titleElement);

            const experienceList = document.createElement('ul');
            experienceList.className = 'bullet-list';

            experienceData.forEach(exp => {
                const listItem = document.createElement('li');

                // Company and location
                const headerDiv = document.createElement('div');
                headerDiv.className = 'entry-header';

                const companyElement = document.createElement('span');
                companyElement.className = 'entry-title';
                companyElement.textContent = exp.company;
                headerDiv.appendChild(companyElement);

                const locationElement = document.createElement('span');
                locationElement.className = 'entry-location';
                locationElement.textContent = exp.location || '';
                headerDiv.appendChild(locationElement);

                listItem.appendChild(headerDiv);

                // Job title
                const titleElement = document.createElement('div');
                titleElement.className = 'entry-subtitle';
                titleElement.textContent = exp.title;
                listItem.appendChild(titleElement);

                // Date range
                if (exp.startDate || exp.endDate) {
                    const dateElement = document.createElement('div');
                    dateElement.className = 'entry-subtitle';
                    dateElement.style.textAlign = 'right';
                    dateElement.textContent = `${exp.startDate || ''} - ${exp.endDate || ''}`;
                    listItem.appendChild(dateElement);
                }

                // Responsibilities/achievements
                if (exp.bullets && exp.bullets.length > 0) {
                    const bulletsList = document.createElement('ul');
                    bulletsList.className = 'sub-bullet-list';

                    exp.bullets.forEach(bullet => {
                        if (bullet.trim()) {
                            const bulletItem = document.createElement('li');
                            bulletItem.textContent = bullet;
                            bulletsList.appendChild(bulletItem);
                        }
                    });

                    if (bulletsList.children.length > 0) {
                        listItem.appendChild(bulletsList);
                    }
                }

                experienceList.appendChild(listItem);
            });

            sectionDiv.appendChild(experienceList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render projects section
        function renderProjectsSection(projectsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'PROJECTS';
            sectionDiv.appendChild(titleElement);

            const projectsList = document.createElement('ul');
            projectsList.className = 'bullet-list';

            projectsData.forEach(project => {
                const listItem = document.createElement('li');

                // Project name
                const nameElement = document.createElement('div');
                nameElement.className = 'entry-title';
                nameElement.textContent = project.name || project.project_name;
                listItem.appendChild(nameElement);

                // Project role (if available)
                if (project.role) {
                    const roleElement = document.createElement('div');
                    roleElement.className = 'entry-subtitle';
                    roleElement.style.fontStyle = 'italic';
                    roleElement.textContent = `Role: ${project.role}`;
                    listItem.appendChild(roleElement);
                }

                // Project description
                if (project.description) {
                    const descElement = document.createElement('div');
                    descElement.style.marginTop = '3px';
                    descElement.textContent = project.description;
                    listItem.appendChild(descElement);
                }

                projectsList.appendChild(listItem);
            });

            sectionDiv.appendChild(projectsList);
            resumeContainer.appendChild(sectionDiv);
        }

        // Render skills section
        function renderSkillsSection(skillsData) {
            const sectionDiv = document.createElement('section');
            sectionDiv.className = 'resume-section';

            // Create section title
            const titleElement = document.createElement('h2');
            titleElement.className = 'section-title';
            titleElement.textContent = 'PROGRAMMING SKILLS';
            sectionDiv.appendChild(titleElement);

            // Check if skills are categorized or flat list
            if (typeof skillsData === 'string') {
                // Single string of skills
                const skillsElement = document.createElement('div');
                skillsElement.textContent = skillsData;
                sectionDiv.appendChild(skillsElement);
            } else if (Array.isArray(skillsData)) {
                // Array of skills
                const skillsList = document.createElement('ul');
                skillsList.className = 'bullet-list';

                skillsData.forEach(skill => {
                    const listItem = document.createElement('li');
                    listItem.textContent = skill;
                    skillsList.appendChild(listItem);
                });

                sectionDiv.appendChild(skillsList);
            } else {
                // Object with categorized skills
                const skillsContainer = document.createElement('div');
                skillsContainer.className = 'skills-list';

                for (const [category, skills] of Object.entries(skillsData)) {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'skill-category';

                    const categoryTitle = document.createElement('span');
                    categoryTitle.className = 'skill-category-title';
                    categoryTitle.textContent = `${category}: `;
                    categoryDiv.appendChild(categoryTitle);

                    const skillsList = document.createElement('span');
                    skillsList.textContent = Array.isArray(skills) ? skills.join(', ') : skills;
                    categoryDiv.appendChild(skillsList);

                    skillsContainer.appendChild(categoryDiv);
                }

                sectionDiv.appendChild(skillsContainer);
            }

            resumeContainer.appendChild(sectionDiv);
        }
    </script>
</body>
</html>
