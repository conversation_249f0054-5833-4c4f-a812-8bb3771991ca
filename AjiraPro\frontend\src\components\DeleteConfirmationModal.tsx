import React, { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Button } from './ui';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  fileName: string;
  isDeleting?: boolean;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  isDeleting = false
}) => {
  const { isDark } = useTheme();

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && !isDeleting) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose, isDeleting]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={!isDeleting ? onClose : undefined}
      />
      
      {/* Modal */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div 
          className={`relative w-full max-w-md rounded-lg shadow-xl ${
            isDark ? 'bg-dark-200' : 'bg-white'
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`flex items-center justify-between p-6 border-b ${
            isDark ? 'border-dark-400' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className={`flex-shrink-0 w-10 h-10 mx-auto flex items-center justify-center rounded-full ${
                isDark ? 'bg-red-900' : 'bg-red-100'
              }`}>
                <svg className={`w-6 h-6 ${isDark ? 'text-red-400' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Delete Resume
                </h3>
              </div>
            </div>
            {!isDeleting && (
              <button
                onClick={onClose}
                className={`p-2 rounded-full transition-colors ${
                  isDark 
                    ? 'hover:bg-dark-400 text-gray-300 hover:text-white' 
                    : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                }`}
                aria-label="Close"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
              Are you sure you want to delete <span className="font-semibold">{fileName}</span>? 
              This action cannot be undone and will permanently remove the resume from your account.
            </p>
          </div>

          {/* Actions */}
          <div className={`flex justify-end space-x-3 p-6 border-t ${
            isDark ? 'border-dark-400' : 'border-gray-200'
          }`}>
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={onConfirm}
              isLoading={isDeleting}
              className={isDark 
                ? 'bg-red-600 hover:bg-red-700 border-red-600' 
                : 'bg-red-600 hover:bg-red-700 text-white'
              }
            >
              {isDeleting ? 'Deleting...' : 'Delete Resume'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
