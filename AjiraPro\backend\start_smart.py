#!/usr/bin/env python3
"""
Smart startup script that detects service type and starts appropriate process
"""

import os
import sys
import subprocess

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def detect_service_type():
    """Detect if this is a worker or web service based on environment"""
    
    # Check for Railway service name
    railway_service = os.getenv("RAILWAY_SERVICE_NAME", "")
    
    # Check for custom environment variable
    service_type = os.getenv("SERVICE_TYPE", "")
    
    # Check if PORT is set (web services have PORT, workers typically don't)
    port = os.getenv("PORT")
    
    print(f"🔍 Service Detection:")
    print(f"   RAILWAY_SERVICE_NAME: {railway_service}")
    print(f"   SERVICE_TYPE: {service_type}")
    print(f"   PORT: {port}")
    
    # Decision logic
    if "worker" in railway_service.lower():
        return "worker"
    elif service_type.lower() == "worker":
        return "worker"
    elif not port:  # No PORT usually means worker
        return "worker"
    else:
        return "web"

def start_web_service():
    """Start FastAPI web server"""
    print("🚀 Starting FastAPI Web Server...")
    
    port = int(os.getenv("PORT", 8000))
    
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True
    )

def start_worker_service():
    """Start Celery worker"""
    print("🔧 Starting Celery Worker...")
    
    # Run celery worker
    subprocess.run([
        "celery", "-A", "app.celery_worker.celery", 
        "worker", "--loglevel=info", "--concurrency=2"
    ])

def main():
    """Main entry point"""
    print("🎯 FajiraPro Smart Startup")
    print("=" * 40)
    
    service_type = detect_service_type()
    print(f"📋 Detected service type: {service_type.upper()}")
    print()
    
    if service_type == "worker":
        start_worker_service()
    else:
        start_web_service()

if __name__ == "__main__":
    main()
