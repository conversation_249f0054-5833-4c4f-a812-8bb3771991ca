# Setting Up a Staging Environment for FajiraPro

This document provides instructions for setting up a staging environment for the FajiraPro project, allowing for testing changes before deploying to production.

## Overview

A staging environment is essential for testing new features and changes before they reach production. For FajiraPro, we'll set up staging environments for both the frontend (Cloudflare Pages) and backend (Railway).

## Frontend Staging Environment (Cloudflare Pages)

### Step 1: Access Your Cloudflare Dashboard
1. Log in to your Cloudflare account
2. Navigate to **Pages** in the sidebar
3. Select your project (fajirapro)

### Step 2: Configure Branch Deployments
1. Go to the **Settings** tab
2. Scroll down to find **Branch Deployments** or **Build Configuration**
3. Look for **Production branch** and **Preview branches** settings

### Step 3: Add `develop` as a Preview Branch
1. In the **Preview branches** section, add `develop`
2. This tells Cloudflare to create deployments for the `develop` branch
3. Save your changes

### Step 4: Configure Environment Variables (if needed)
1. Go to the **Environment variables** section
2. You may want to add different variables for preview deployments
3. For example, set `REACT_APP_API_URL` to point to your staging backend

### Step 5: Trigger a Deployment
1. Push a small change to your `develop` branch
2. Cloudflare will automatically create a preview deployment
3. You'll find the URL in the **Deployments** tab (usually `develop.fajirapro.pages.dev`)

## Backend Staging Environment (Railway)

### Step 1: Access Your Railway Dashboard
1. Log in to your Railway account
2. Navigate to your project (AjiraProMax)

### Step 2: Create a New Environment
1. Click on **Settings** in your project
2. Go to the **Environments** section
3. Click **+ New Environment**
4. Name it `staging` or `develop`

### Step 3: Configure the Environment
1. In the new environment, go to **Settings**
2. Under **Deploy Triggers**, add your GitHub repository
3. Set the branch to `develop`
4. Save your changes

### Step 4: Configure Environment Variables
1. In your staging environment, go to **Variables**
2. Set up environment variables specific to staging
3. Make sure these are different from production (especially database connections)

### Step 5: Deploy Your Services
1. For each service in your project (FastAPI, Redis, Celery):
   - Go to the service settings
   - Make sure it's configured to deploy in the staging environment
   - You may need to manually deploy the first time

### Step 6: Verify Deployment
1. Push a change to your `develop` branch
2. Railway should automatically deploy to your staging environment
3. Find the URL in the service details (usually something like `fajirapro-staging.up.railway.app`)

## Connecting Frontend and Backend Staging Environments

### Update Frontend Environment Variables
1. In Cloudflare Pages, go to your project settings
2. Find **Environment variables**
3. Add a variable for preview deployments:
   - Name: `REACT_APP_API_URL` (or whatever you use)
   - Value: Your Railway staging backend URL
4. Save changes

### Test the Connection
1. Deploy both environments
2. Access your Cloudflare preview URL
3. Verify that the frontend can communicate with the backend

## Testing Workflow with Staging Environment

Once your staging environment is set up, follow this workflow for testing:

1. Develop features in feature branches
2. Create pull requests to merge features into the `develop` branch
3. After merging, changes are automatically deployed to the staging environment
4. Test the changes in the staging environment
5. If everything works correctly, create a pull request from `develop` to `main`
6. After merging to `main`, changes are deployed to production

This workflow ensures that changes are thoroughly tested before reaching production, reducing the risk of issues affecting real users.

## Best Practices

1. **Keep Environments Separate**:
   - Use different databases for staging and production
   - Use different API keys for third-party services

2. **Document Your URLs**:
   - Keep a record of your staging URLs for easy access
   - Share them with team members who need to test

3. **Regular Testing**:
   - Test your staging environment regularly
   - Make sure it stays in sync with production

4. **Clean Up Old Deployments**:
   - Both Cloudflare and Railway allow you to delete old deployments
   - This can save resources and keep things organized
