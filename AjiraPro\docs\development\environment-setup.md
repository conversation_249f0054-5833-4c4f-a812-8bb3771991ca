# Development Environment Setup

This document provides detailed instructions for setting up your development environment for the AjiraPro project.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18 or later
  - Download from [nodejs.org](https://nodejs.org/)
  - Verify with `node --version`

- **npm**: Version 9 or later (comes with Node.js)
  - Verify with `npm --version`

- **Python**: Version 3.10 or later
  - Download from [python.org](https://python.org/)
  - Verify with `python --version`

- **Git**: Latest version
  - Download from [git-scm.com](https://git-scm.com/)
  - Verify with `git --version`

- **Visual Studio Code**: Latest version
  - Download from [code.visualstudio.com](https://code.visualstudio.com/)

## VS Code Extensions

Install the following VS Code extensions for the best development experience:

1. **ESLint**: JavaScript linting
   - Extension ID: `dbaeumer.vscode-eslint`

2. **Prettier**: Code formatting
   - Extension ID: `esbenp.prettier-vscode`

3. **Tailwind CSS IntelliSense**: Autocomplete for Tailwind CSS
   - Extension ID: `bradlc.vscode-tailwindcss`

4. **Python**: Python language support
   - Extension ID: `ms-python.python`

5. **Pylance**: Python language server
   - Extension ID: `ms-python.vscode-pylance`

6. **FastAPI**: FastAPI support
   - Extension ID: `ms-python.fastapi`

7. **Thunder Client**: API testing
   - Extension ID: `rangav.vscode-thunder-client`

8. **GitLens**: Git integration
   - Extension ID: `eamodio.gitlens`

9. **Supabase**: Supabase integration
   - Extension ID: `supabase.supabase-vscode`

## Cloning the Repository

```bash
git clone https://github.com/ProDevDenis/fAjiraPro.git
cd fAjiraPro
```

## Frontend Setup

1. **Navigate to the frontend directory**:
   ```bash
   cd AjiraPro/frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` and update the following variables:
   ```
   REACT_APP_SUPABASE_URL=your_supabase_url
   REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
   REACT_APP_API_URL=http://localhost:8000
   ```

4. **Start the development server**:
   ```bash
   npm start
   ```
   
   The frontend will be available at http://localhost:3000

## Backend Setup

1. **Navigate to the backend directory**:
   ```bash
   cd AjiraPro/backend
   ```

2. **Install Node.js dependencies** (for scripts):
   ```bash
   npm install
   ```

3. **Set up Python virtual environment**:
   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

4. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

5. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and update the following variables:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_KEY=your_supabase_service_key
   OPENAI_API_KEY=your_openai_api_key
   ```

6. **Start the development server**:
   ```bash
   uvicorn app.main:app --reload
   ```
   
   The backend API will be available at http://localhost:8000

## Supabase Setup

1. **Create a Supabase project**:
   - Go to [supabase.com](https://supabase.com) and sign up/login
   - Create a new project
   - Note your project URL and anon key

2. **Set up database schema**:
   - The schema will be automatically set up when you run the backend for the first time
   - Alternatively, you can run the SQL scripts in `AjiraPro/backend/supabase/schema.sql`

3. **Create storage buckets**:
   ```bash
   cd AjiraPro/backend
   npm run create-buckets
   ```

## Working with the Codebase

### Frontend Development

- **Component Structure**: Components are organized in `src/components`
- **Routing**: Routes are defined in `src/App.tsx`
- **API Calls**: API calls are made using the Supabase client in `src/utils/supabase.ts`
- **Styling**: Styling is done using Tailwind CSS

### Backend Development

- **API Routes**: Routes are defined in `app/routers/`
- **Database Models**: Models are defined in `app/models/`
- **Services**: Business logic is in `app/services/`
- **Supabase Integration**: Supabase client is in `app/services/supabase.py`

## Common Development Tasks

### Adding a New Frontend Component

1. Create a new file in `src/components/`
2. Import and use the component in your pages
3. Add styles using Tailwind CSS classes

### Adding a New API Endpoint

1. Create or modify a router file in `app/routers/`
2. Define the endpoint with appropriate HTTP method
3. Implement the endpoint logic
4. Register the router in `app/main.py`

### Running Tests

- **Frontend Tests**:
  ```bash
  cd AjiraPro/frontend
  npm test
  ```

- **Backend Tests**:
  ```bash
  cd AjiraPro/backend
  pytest
  ```

## Troubleshooting

### Frontend Issues

- **Node.js version conflicts**: Use nvm to manage Node.js versions
- **Dependency issues**: Use `--legacy-peer-deps` flag with npm install
- **Environment variable issues**: Ensure .env.local file is properly set up

### Backend Issues

- **Python version conflicts**: Use pyenv to manage Python versions
- **Virtual environment issues**: Ensure virtual environment is activated
- **Database connection issues**: Check Supabase credentials

## Getting Help

If you encounter any issues not covered in this document, please:

1. Check the existing GitHub issues
2. Create a new issue with detailed information about the problem
3. Reach out to the development team on the project communication channels
