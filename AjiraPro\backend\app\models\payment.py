from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from uuid import UUID

class PaymentCreate(BaseModel):
    amount: float
    currency: str = "USD"
    payment_type: str = Field(..., description="Type of payment: subscription, one_time, etc.")
    plan_id: Optional[str] = None
    redirect_url: Optional[str] = None

class PaymentVerify(BaseModel):
    transaction_id: str

class PaymentResponse(BaseModel):
    id: UUID
    user_id: UUID
    amount: float
    currency: str
    payment_type: str
    plan_id: Optional[str] = None
    transaction_id: Optional[str] = None
    status: str = Field(..., description="Status of the payment: pending, completed, failed, etc.")
    payment_method: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
