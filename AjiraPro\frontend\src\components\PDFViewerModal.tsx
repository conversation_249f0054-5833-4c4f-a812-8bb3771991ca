import React, { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import EnhancedPDFViewer from './EnhancedPDFViewer';

interface PDFViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  filePath: string;
  fileName: string;
}

const PDFViewerModal: React.FC<PDFViewerModalProps> = ({
  isOpen,
  onClose,
  filePath,
  fileName
}) => {
  const { isDark } = useTheme();

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div
          className={`relative w-full max-w-6xl h-full max-h-[90vh] rounded-lg shadow-xl ${
            isDark ? 'bg-dark-200' : 'bg-white'
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`flex items-center justify-between p-4 border-b ${
            isDark ? 'border-dark-400' : 'border-gray-200'
          }`}>
            <h2 className={`text-lg font-semibold ${
              isDark ? 'text-white' : 'text-gray-900'
            }`}>
              {fileName}
            </h2>
            <button
              onClick={onClose}
              className={`p-2 rounded-full transition-colors ${
                isDark
                  ? 'hover:bg-dark-400 text-gray-300 hover:text-white'
                  : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
              }`}
              aria-label="Close"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* PDF Viewer */}
          <div className="flex-1 h-full" style={{ height: 'calc(90vh - 80px)' }}>
            <EnhancedPDFViewer
              filePath={filePath}
              fileName={fileName}
              className="w-full h-full"
              onError={(error) => {
                // Could show error toast here
                // Error is handled by the PDF viewer component
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFViewerModal;
