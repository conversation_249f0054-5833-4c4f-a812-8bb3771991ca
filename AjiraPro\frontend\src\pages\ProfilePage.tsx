import React, { useState, useRef, ChangeEvent } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { supabase } from '../utils/supabase';

const ProfilePage: React.FC = () => {
  const { profile, updateProfile, user } = useAuth();
  const { isDark } = useTheme();
  const [isEditing, setIsEditing] = useState(false);
  const [fullName, setFullName] = useState(profile?.full_name || '');
  const [phoneNumber, setPhoneNumber] = useState(profile?.phone_number || '');
  const [bio, setBio] = useState(profile?.bio || '');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(profile?.avatar_url || null);
  const [uploading, setUploading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    try {
      const { error } = await updateProfile({
        full_name: fullName,
        phone_number: phoneNumber,
        bio,
      });

      if (error) {
        throw error;
      }

      setMessage({ text: 'Profile updated successfully!', type: 'success' });
      setIsEditing(false);
    } catch (error: any) {
      setMessage({ text: error.message || 'Failed to update profile', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle avatar upload
  const handleAvatarUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];
    const fileExt = file.name.split('.').pop();
    const fileName = `${user?.id}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${fileName}`;

    setUploading(true);
    setMessage(null);

    try {
      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data } = supabase.storage.from('avatars').getPublicUrl(filePath);
      const publicUrl = data.publicUrl;

      // Update profile with new avatar URL
      const { error: updateError } = await updateProfile({
        avatar_url: publicUrl,
      });

      if (updateError) {
        throw updateError;
      }

      setAvatarUrl(publicUrl);
      setMessage({ text: 'Avatar updated successfully!', type: 'success' });
    } catch (error: any) {
      setMessage({ text: error.message || 'Failed to upload avatar', type: 'error' });
    } finally {
      setUploading(false);
    }
  };

  // Trigger file input click
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  if (!profile) {
    return (
      <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className={`animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 ${isDark ? 'border-neon-cyan' : 'border-blue-500'}`}></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} py-12`}>
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow'} overflow-hidden sm:rounded-lg`}>
          <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
              <h2 className={`text-2xl font-bold leading-7 ${isDark ? 'text-white' : 'text-gray-900'}`}>Profile</h2>
              <p className={`mt-1 max-w-2xl text-sm ${isDark ? 'text-gray-300' : 'text-gray-500'}`}>
                Your personal information and settings
              </p>
            </div>
            {!isEditing && (
              <button
                onClick={() => setIsEditing(true)}
                className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md shadow-sm ${
                  isDark
                    ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                    : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-offset-2`}
              >
                Edit Profile
              </button>
            )}
          </div>

          {message && (
            <div
              className={`px-4 py-3 ${
                message.type === 'success'
                  ? isDark ? 'bg-green-900/30 text-green-400 border-l-4 border-green-700' : 'bg-green-50 text-green-800'
                  : isDark ? 'bg-red-900/30 text-red-400 border-l-4 border-red-700' : 'bg-red-50 text-red-800'
              }`}
            >
              {message.text}
            </div>
          )}

          <div className={`border-t ${isDark ? 'border-dark-400' : 'border-gray-200'} px-4 py-5 sm:p-6`}>
            <div className="flex flex-col sm:flex-row items-center mb-6">
              <div className="mb-4 sm:mb-0 sm:mr-6">
                <div
                  onClick={handleAvatarClick}
                  className={`h-24 w-24 rounded-full overflow-hidden ${isDark ? 'bg-dark-300' : 'bg-gray-100'} flex items-center justify-center cursor-pointer ${
                    uploading ? 'opacity-50' : ''
                  }`}
                >
                  {avatarUrl ? (
                    <img
                      src={avatarUrl}
                      alt="Profile"
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <svg
                      className={`h-12 w-12 ${isDark ? 'text-gray-600' : 'text-gray-400'}`}
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  )}
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleAvatarUpload}
                  accept="image/*"
                  className="hidden"
                  disabled={uploading}
                />
                <div className={`mt-2 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} text-center`}>
                  Click to change avatar
                </div>
              </div>

              {isEditing ? (
                <form onSubmit={handleSubmit} className="w-full">
                  <div className="space-y-4">
                    <div>
                      <label
                        htmlFor="fullName"
                        className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}
                      >
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm ${
                          isDark
                            ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500 focus:ring-neon-cyan focus:border-neon-cyan'
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="email"
                        className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}
                      >
                        Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={profile.email}
                        disabled
                        className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 sm:text-sm ${
                          isDark
                            ? 'bg-dark-400 border-dark-500 text-gray-400'
                            : 'bg-gray-50 border-gray-300 text-gray-500'
                        }`}
                      />
                      <p className={`mt-1 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                        Email cannot be changed
                      </p>
                    </div>

                    <div>
                      <label
                        htmlFor="phoneNumber"
                        className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}
                      >
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phoneNumber"
                        value={phoneNumber}
                        onChange={(e) => setPhoneNumber(e.target.value)}
                        className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm ${
                          isDark
                            ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500 focus:ring-neon-cyan focus:border-neon-cyan'
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="bio"
                        className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}
                      >
                        Bio
                      </label>
                      <textarea
                        id="bio"
                        value={bio}
                        onChange={(e) => setBio(e.target.value)}
                        rows={3}
                        className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm ${
                          isDark
                            ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500 focus:ring-neon-cyan focus:border-neon-cyan'
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                      />
                    </div>

                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditing(false);
                          setFullName(profile.full_name || '');
                          setPhoneNumber(profile.phone_number || '');
                          setBio(profile.bio || '');
                        }}
                        className={`inline-flex items-center px-4 py-2 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                          isDark
                            ? 'border-dark-400 bg-dark-300 text-gray-300 hover:bg-dark-400 focus:ring-dark-500'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500'
                        }`}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                          isDark
                            ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                            : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                        } disabled:opacity-50`}
                      >
                        {isLoading ? 'Saving...' : 'Save'}
                      </button>
                    </div>
                  </div>
                </form>
              ) : (
                <div className="w-full">
                  <dl className={`divide-y ${isDark ? 'divide-dark-400' : 'divide-gray-200'}`}>
                    <div className="py-3 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                      <dt className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Full name</dt>
                      <dd className={`mt-1 text-sm ${isDark ? 'text-white' : 'text-gray-900'} sm:mt-0 sm:col-span-2`}>
                        {profile.full_name || 'Not provided'}
                      </dd>
                    </div>
                    <div className="py-3 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                      <dt className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Email address</dt>
                      <dd className={`mt-1 text-sm ${isDark ? 'text-white' : 'text-gray-900'} sm:mt-0 sm:col-span-2`}>
                        {profile.email}
                      </dd>
                    </div>
                    <div className="py-3 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                      <dt className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Phone number</dt>
                      <dd className={`mt-1 text-sm ${isDark ? 'text-white' : 'text-gray-900'} sm:mt-0 sm:col-span-2`}>
                        {profile.phone_number || 'Not provided'}
                      </dd>
                    </div>
                    <div className="py-3 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                      <dt className={`text-sm font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Bio</dt>
                      <dd className={`mt-1 text-sm ${isDark ? 'text-white' : 'text-gray-900'} sm:mt-0 sm:col-span-2`}>
                        {profile.bio || 'Not provided'}
                      </dd>
                    </div>
                  </dl>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
