import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../utils/supabase';

const MagicLinkSuccess: React.FC = () => {
  const [message, setMessage] = useState<string>('Signing you in...');
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleMagicLinkRedirect = async () => {
      try {
        // Check for error parameters that indicate an expired token
        const searchParams = new URLSearchParams(window.location.search);
        const errorCode = searchParams.get('error_code');
        const errorDescription = searchParams.get('error_description');

        if (errorCode === 'otp_expired' || errorCode === 'access_denied' ||
            (errorDescription && (errorDescription.includes('expired') || errorDescription.includes('invalid')))) {
          // Redirect to the expired token page with the error information
          navigate(`/auth/token-expired?error_code=${errorCode || ''}&error_description=${errorDescription || ''}`);
          return;
        }

        // Check if we have a hash fragment in the URL
        const hash = window.location.hash;

        // Process the hash to get the session
        setMessage('Verifying your login...');

        // Get the current session
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        if (data.session) {
          // We have a session, redirect to dashboard
          setMessage('Success! Redirecting to your dashboard...');

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            navigate('/dashboard');
          }, 1500);
        } else if (hash) {
          // We have a hash but no session, try to exchange the token for a session
          // This might happen if the session wasn't automatically created

          // Extract the access token from the hash if it exists
          let accessToken = null;
          if (hash.includes('access_token=')) {
            const hashParams = new URLSearchParams(hash.substring(1)); // Remove the # character
            accessToken = hashParams.get('access_token');
          }

          if (accessToken) {
            // Try to set the session with the access token
            const { data: sessionData, error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: '', // We don't have a refresh token in this flow
            });

            if (sessionError) {
              throw sessionError;
            }

            if (sessionData.session) {
              // Session created successfully, redirect to dashboard
              setMessage('Success! Redirecting to your dashboard...');

              // Redirect to dashboard after a short delay
              setTimeout(() => {
                navigate('/dashboard');
              }, 1500);
              return;
            }
          }

          // If we get here, we couldn't create a session
          setError('Unable to sign in. Please try again.');
        } else {
          // No session and no hash, can't proceed
          setError('Invalid or missing authentication token');
        }
      } catch (error: any) {
        setError(error.message || 'An error occurred during sign in');
      }
    };

    handleMagicLinkRedirect();
  }, [navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {error ? 'Sign In Failed' : 'Magic Link Sign In'}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {error ? (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                  <div className="mt-4">
                    <button
                      onClick={() => navigate('/login')}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Back to Sign In
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-700">{message}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MagicLinkSuccess;
