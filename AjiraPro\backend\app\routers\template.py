from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from ..models.template import TemplateResponse
from ..services.template import TemplateService
from ..core.security import get_current_user
from ..models.user import User

router = APIRouter(
    prefix="/templates",
    tags=["templates"],
    responses={404: {"description": "Not found"}},
)

@router.get("/resume", response_model=List[TemplateResponse])
async def get_resume_templates(
    category: str = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    template_service: TemplateService = Depends(),
):
    """
    Get all resume templates, optionally filtered by category.
    """
    return await template_service.get_resume_templates(category, skip, limit)


@router.get("/resume/{template_id}", response_model=TemplateResponse)
async def get_resume_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    template_service: TemplateService = Depends(),
):
    """
    Get a specific resume template by ID.
    """
    template = await template_service.get_resume_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    return template


@router.get("/cv", response_model=List[TemplateResponse])
async def get_cv_templates(
    category: str = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    template_service: TemplateService = Depends(),
):
    """
    Get all CV templates, optionally filtered by category.
    """
    return await template_service.get_cv_templates(category, skip, limit)


@router.get("/cv/{template_id}", response_model=TemplateResponse)
async def get_cv_template(
    template_id: str,
    current_user: User = Depends(get_current_user),
    template_service: TemplateService = Depends(),
):
    """
    Get a specific CV template by ID.
    """
    template = await template_service.get_cv_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    return template
