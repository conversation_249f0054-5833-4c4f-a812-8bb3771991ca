-- Migration: Add scoring fields to resumes table and create scoring_history table
-- Date: 2024-01-XX
-- Description: Adds comprehensive scoring fields for AI-powered resume analysis

-- Add new scoring columns to resumes table
ALTER TABLE resumes 
ADD COLUMN IF NOT EXISTS overall_score DECIMAL(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
ADD COLUMN IF NOT EXISTS claude_score DECIMAL(5,2) CHECK (claude_score >= 0 AND claude_score <= 100),
ADD COLUMN IF NOT EXISTS openai_score DECIMAL(5,2) CHECK (openai_score >= 0 AND openai_score <= 100),
ADD COLUMN IF NOT EXISTS career_overview_score DECIMAL(5,2) CHECK (career_overview_score >= 0 AND career_overview_score <= 100),
ADD COLUMN IF NOT EXISTS experience_score DECIMAL(5,2) CHECK (experience_score >= 0 AND experience_score <= 100),
ADD COLUMN IF NOT EXISTS education_score DECIMAL(5,2) CHECK (education_score >= 0 AND education_score <= 100),
ADD COLUMN IF NOT EXISTS additional_qualifications_score DECIMAL(5,2) CHECK (additional_qualifications_score >= 0 AND additional_qualifications_score <= 100),
ADD COLUMN IF NOT EXISTS content_quality_score DECIMAL(5,2) CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
ADD COLUMN IF NOT EXISTS scoring_feedback JSONB,
ADD COLUMN IF NOT EXISTS last_scored_at TIMESTAMP WITH TIME ZONE;

-- Create scoring_history table
CREATE TABLE IF NOT EXISTS scoring_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  resume_id UUID REFERENCES resumes(id) ON DELETE CASCADE NOT NULL,
  overall_score DECIMAL(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  claude_score DECIMAL(5,2) CHECK (claude_score >= 0 AND claude_score <= 100),
  openai_score DECIMAL(5,2) CHECK (openai_score >= 0 AND openai_score <= 100),
  career_overview_score DECIMAL(5,2) CHECK (career_overview_score >= 0 AND career_overview_score <= 100),
  experience_score DECIMAL(5,2) CHECK (experience_score >= 0 AND experience_score <= 100),
  education_score DECIMAL(5,2) CHECK (education_score >= 0 AND education_score <= 100),
  additional_qualifications_score DECIMAL(5,2) CHECK (additional_qualifications_score >= 0 AND additional_qualifications_score <= 100),
  content_quality_score DECIMAL(5,2) CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
  scoring_feedback JSONB,
  scoring_version TEXT DEFAULT '1.0',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Add RLS policies for scoring_history table
ALTER TABLE scoring_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view scoring history for their own resumes" ON scoring_history
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = scoring_history.resume_id
    )
  );

CREATE POLICY "Users can insert scoring history for their own resumes" ON scoring_history
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = scoring_history.resume_id
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scoring_history_resume_id ON scoring_history(resume_id);
CREATE INDEX IF NOT EXISTS idx_scoring_history_created_at ON scoring_history(created_at);
CREATE INDEX IF NOT EXISTS idx_resumes_last_scored_at ON resumes(last_scored_at);
CREATE INDEX IF NOT EXISTS idx_resumes_overall_score ON resumes(overall_score);

-- Create function to automatically save scoring history when resume scores are updated
CREATE OR REPLACE FUNCTION public.save_scoring_history()
RETURNS TRIGGER AS $$
BEGIN
  -- Only save to history if scoring fields have changed
  IF (OLD.overall_score IS DISTINCT FROM NEW.overall_score OR
      OLD.claude_score IS DISTINCT FROM NEW.claude_score OR
      OLD.openai_score IS DISTINCT FROM NEW.openai_score OR
      OLD.career_overview_score IS DISTINCT FROM NEW.career_overview_score OR
      OLD.experience_score IS DISTINCT FROM NEW.experience_score OR
      OLD.education_score IS DISTINCT FROM NEW.education_score OR
      OLD.additional_qualifications_score IS DISTINCT FROM NEW.additional_qualifications_score OR
      OLD.content_quality_score IS DISTINCT FROM NEW.content_quality_score) THEN
    
    INSERT INTO public.scoring_history (
      resume_id,
      overall_score,
      claude_score,
      openai_score,
      career_overview_score,
      experience_score,
      education_score,
      additional_qualifications_score,
      content_quality_score,
      scoring_feedback,
      scoring_version
    ) VALUES (
      NEW.id,
      NEW.overall_score,
      NEW.claude_score,
      NEW.openai_score,
      NEW.career_overview_score,
      NEW.experience_score,
      NEW.education_score,
      NEW.additional_qualifications_score,
      NEW.content_quality_score,
      NEW.scoring_feedback,
      '1.0'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically save scoring history
CREATE TRIGGER on_resume_scoring_update
  AFTER UPDATE ON public.resumes
  FOR EACH ROW
  WHEN (NEW.overall_score IS NOT NULL)
  EXECUTE FUNCTION public.save_scoring_history();

-- Add comments for documentation
COMMENT ON TABLE scoring_history IS 'Stores historical scoring data for resumes to track improvements over time';
COMMENT ON COLUMN resumes.overall_score IS 'Overall resume score (0-100) calculated from Claude and OpenAI scores';
COMMENT ON COLUMN resumes.claude_score IS 'Format and structure score from Claude (25% of overall score)';
COMMENT ON COLUMN resumes.openai_score IS 'Content score from OpenAI (75% of overall score)';
COMMENT ON COLUMN resumes.scoring_feedback IS 'Detailed feedback and suggestions from AI analysis';
COMMENT ON COLUMN resumes.last_scored_at IS 'Timestamp of the last scoring analysis';
