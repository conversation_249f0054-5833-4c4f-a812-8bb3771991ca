"""
Claude <PERSON> Service

Handles resume parsing and format scoring using Claude Sonnet 3.5
"""

import json
import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime

from ..services.ai import ai_service, AIServiceError
from ..models.resume import FeedbackIssue

logger = logging.getLogger(__name__)

class ClaudeParsingError(Exception):
    """Exception raised when <PERSON> parsing fails"""
    pass

class ClaudeParser:
    """Service for parsing resumes and scoring format using <PERSON>"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = "claude-3-5-sonnet-20241022"  # Claude Sonnet 3.5
    
    async def parse_resume_content(self, extracted_text: str, filename: str) -> Dict[str, Any]:
        """
        Parse resume text into structured JSON format
        
        Args:
            extracted_text: Raw text extracted from resume file
            filename: Original filename for context
            
        Returns:
            Structured resume data as JSON
            
        Raises:
            ClaudeParsingError: If parsing fails
        """
        try:
            parsing_prompt = self._create_parsing_prompt(extracted_text, filename)
            
            messages = [
                {
                    "role": "system",
                    "content": "You are an expert resume parser. Extract and structure resume content into standardized JSON format. Always respond with valid JSON only."
                },
                {
                    "role": "user", 
                    "content": parsing_prompt
                }
            ]
            
            response = await ai_service.call_claude(messages, model=self.model)
            
            # Extract content from Claude response
            if "content" in response and len(response["content"]) > 0:
                parsed_content = response["content"][0]["text"]
            else:
                raise ClaudeParsingError("Invalid response format from Claude")
            
            # Parse JSON response
            try:
                structured_data = json.loads(parsed_content)
                return self._validate_parsed_content(structured_data)
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse Claude JSON response: {str(e)}")
                raise ClaudeParsingError(f"Invalid JSON response from Claude: {str(e)}")
                
        except AIServiceError as e:
            self.logger.error(f"Claude API error during parsing: {str(e)}")
            raise ClaudeParsingError(f"Claude API error: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error during parsing: {str(e)}")
            raise ClaudeParsingError(f"Parsing failed: {str(e)}")
    
    async def score_format_and_structure(self, parsed_content: Dict[str, Any], original_text: str) -> Tuple[float, Dict[str, Any]]:
        """
        Score resume format and structure based on ATS compatibility
        
        Args:
            parsed_content: Structured resume data from parsing
            original_text: Original extracted text for format analysis
            
        Returns:
            Tuple of (score, feedback_data)
        """
        try:
            scoring_prompt = self._create_format_scoring_prompt(parsed_content, original_text)
            
            messages = [
                {
                    "role": "system",
                    "content": "You are an expert ATS (Applicant Tracking System) analyst. Evaluate resume format and structure for ATS compatibility. Respond with valid JSON only."
                },
                {
                    "role": "user",
                    "content": scoring_prompt
                }
            ]
            
            response = await ai_service.call_claude(messages, model=self.model)
            
            # Extract content from Claude response
            if "content" in response and len(response["content"]) > 0:
                scoring_result = response["content"][0]["text"]
            else:
                raise ClaudeParsingError("Invalid response format from Claude")
            
            # Parse JSON response
            try:
                scoring_data = json.loads(scoring_result)
                score = float(scoring_data.get("overall_score", 0))
                feedback = self._structure_claude_feedback(scoring_data)
                return score, feedback
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.error(f"Failed to parse Claude scoring response: {str(e)}")
                raise ClaudeParsingError(f"Invalid scoring response from Claude: {str(e)}")
                
        except AIServiceError as e:
            self.logger.error(f"Claude API error during scoring: {str(e)}")
            raise ClaudeParsingError(f"Claude API error: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error during scoring: {str(e)}")
            raise ClaudeParsingError(f"Scoring failed: {str(e)}")
    
    def _create_parsing_prompt(self, text: str, filename: str) -> str:
        """Create prompt for resume parsing"""
        return f"""
Parse the following resume text into a structured JSON format. Extract all relevant information and organize it according to the schema below.

RESUME TEXT:
{text}

FILENAME: {filename}

Please extract and structure the information into this JSON format:
{{
  "personal_info": {{
    "name": "Full name",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "location": "City, State/Country",
    "linkedin": "linkedin.com/in/profile",
    "github": "github.com/username",
    "website": "personal-website.com"
  }},
  "professional_summary": "Brief professional overview...",
  "experience": [
    {{
      "title": "Job Title",
      "company": "Company Name",
      "location": "City, State",
      "start_date": "YYYY-MM",
      "end_date": "YYYY-MM or Present",
      "description": "Job responsibilities and achievements...",
      "achievements": ["Achievement 1", "Achievement 2"],
      "technologies": ["Tech1", "Tech2"]
    }}
  ],
  "education": [
    {{
      "degree": "Degree Type",
      "field": "Field of Study",
      "institution": "University/School Name",
      "location": "City, State",
      "graduation_date": "YYYY-MM",
      "gpa": "3.8",
      "honors": ["Magna Cum Laude", "Dean's List"]
    }}
  ],
  "skills": {{
    "technical": ["Python", "JavaScript", "React"],
    "soft": ["Leadership", "Communication"],
    "tools": ["Git", "Docker", "AWS"],
    "languages": ["English", "Spanish"]
  }},
  "certifications": [
    {{
      "name": "Certification Name",
      "issuer": "Issuing Organization",
      "date": "YYYY-MM",
      "expiry": "YYYY-MM",
      "credential_id": "ID123456"
    }}
  ],
  "projects": [
    {{
      "name": "Project Name",
      "description": "Project description...",
      "technologies": ["React", "Node.js"],
      "url": "github.com/user/project",
      "start_date": "YYYY-MM",
      "end_date": "YYYY-MM"
    }}
  ],
  "additional_sections": {{
    "volunteer_work": [],
    "publications": [],
    "awards": [],
    "languages": [],
    "interests": []
  }}
}}

IMPORTANT:
- Extract only information that is explicitly present in the text
- Use null for missing information
- Ensure all dates are in YYYY-MM format
- For current positions, use "Present" as end_date
- Be precise and don't infer information not explicitly stated
- Return only valid JSON, no additional text or explanations
"""
    
    def _create_format_scoring_prompt(self, parsed_content: Dict[str, Any], original_text: str) -> str:
        """Create prompt for format and structure scoring"""
        return f"""
Analyze this resume for format and structure quality based on ATS (Applicant Tracking System) compatibility. 

PARSED RESUME DATA:
{json.dumps(parsed_content, indent=2)}

ORIGINAL TEXT (for format analysis):
{original_text[:2000]}...

Evaluate the resume based on these criteria and assign scores:

**HIGH SCORE (90-100%) criteria:**
- Uses reverse chronological format (most recent experience first)
- Clear, standard section headings (e.g., "Work Experience," "Education")
- Simple, ATS-friendly fonts (e.g., Arial, Calibri, Times New Roman)
- No tables, columns, images, or graphics
- Consistent date formatting throughout (e.g., MM/YYYY)
- Well-used bullet points in experience sections, max 2 lines per bullet
- Perfect use of whitespace and simple, ATS-friendly formatting
- Contains standard contact information in text (not in header/footer)

**LOW SCORE (10-30%) criteria:**
- Uses creative or functional format with non-standard sections
- Contains images, graphics, charts, or tables
- Uses multiple columns that confuse parsing algorithms
- Includes text boxes or non-standard formatting elements
- Uses uncommon fonts or excessive formatting
- Poor or no use of bullet points, or bullets exceed 2 lines
- Poor use of whitespace and not ATS-friendly formatting
- Critical information placed in headers or footers

Respond with this JSON format:
{{
  "overall_score": 85.5,
  "format_analysis": {{
    "chronological_order": {{
      "score": 90,
      "issues": ["List any issues found"],
      "recommendations": ["Specific recommendations"]
    }},
    "section_headings": {{
      "score": 85,
      "issues": ["Non-standard heading found"],
      "recommendations": ["Use standard headings like 'Work Experience'"]
    }},
    "ats_compatibility": {{
      "score": 80,
      "issues": ["Complex formatting detected"],
      "recommendations": ["Simplify formatting for better ATS parsing"]
    }},
    "date_consistency": {{
      "score": 95,
      "issues": [],
      "recommendations": []
    }},
    "contact_information": {{
      "score": 90,
      "issues": [],
      "recommendations": []
    }},
    "formatting_quality": {{
      "score": 75,
      "issues": ["Inconsistent bullet point usage"],
      "recommendations": ["Use consistent bullet points throughout"]
    }}
  }},
  "detailed_feedback": [
    {{
      "issue": "Specific issue description",
      "severity": "high|medium|low",
      "recommendation": "Specific recommendation",
      "section": "section_name"
    }}
  ]
}}

Return only valid JSON, no additional text.
"""
    
    def _validate_parsed_content(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean parsed content"""
        required_sections = ["personal_info", "experience", "education", "skills"]
        
        for section in required_sections:
            if section not in data:
                data[section] = {} if section in ["personal_info", "skills"] else []
        
        # Ensure personal_info has basic structure
        if not isinstance(data["personal_info"], dict):
            data["personal_info"] = {}
        
        # Ensure arrays are actually arrays
        array_sections = ["experience", "education", "certifications", "projects"]
        for section in array_sections:
            if section in data and not isinstance(data[section], list):
                data[section] = []
        
        return data
    
    def _structure_claude_feedback(self, scoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """Structure Claude feedback into standardized format"""
        feedback_issues = []
        
        # Extract issues from detailed feedback
        if "detailed_feedback" in scoring_data:
            for item in scoring_data["detailed_feedback"]:
                feedback_issues.append({
                    "issue": item.get("issue", ""),
                    "severity": item.get("severity", "medium"),
                    "recommendation": item.get("recommendation", ""),
                    "section": item.get("section", "")
                })
        
        # Extract format analysis issues
        format_analysis = scoring_data.get("format_analysis", {})
        for category, analysis in format_analysis.items():
            if isinstance(analysis, dict) and analysis.get("issues"):
                for issue in analysis["issues"]:
                    feedback_issues.append({
                        "issue": issue,
                        "severity": "medium",
                        "recommendation": analysis.get("recommendations", [""])[0] if analysis.get("recommendations") else "",
                        "section": category
                    })
        
        return {
            "format_issues": feedback_issues,
            "ats_compatibility": {
                "score": scoring_data.get("overall_score", 0),
                "analysis": format_analysis
            },
            "overall_analysis": scoring_data
        }

# Global Claude parser instance
claude_parser = ClaudeParser()

async def get_claude_parser() -> ClaudeParser:
    """Dependency injection for Claude parser"""
    return claude_parser
