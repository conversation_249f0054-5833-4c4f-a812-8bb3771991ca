# Authentication & User Management Documentation

This document provides a comprehensive overview of the authentication and user management system implemented in AjiraPro.

## Table of Contents
1. [Overview](#overview)
2. [Authentication Methods](#authentication-methods)
3. [User Interface Components](#user-interface-components)
4. [Authentication Flow](#authentication-flow)
5. [User Profile Management](#user-profile-management)
6. [Security Considerations](#security-considerations)
7. [Technical Implementation](#technical-implementation)

## Overview

AjiraPro uses Supabase Auth for authentication, providing a secure and flexible authentication system with multiple sign-in methods. The authentication system is integrated throughout the application with protected routes and user profile management.

## Authentication Methods

### Email/Password Authentication
- Traditional email and password sign-up and sign-in
- Password strength requirements enforced
- Email verification required for new accounts
- Password reset functionality

### Magic Link Authentication
- Passwordless authentication via email magic links
- Secure token-based authentication
- Automatic session creation upon successful verification

### OAuth Authentication
- Google authentication integration
- Seamless third-party authentication flow
- Profile information syncing from OAuth providers

## User Interface Components

### AuthModal Component
- Single modal with toggle between login and signup forms
- Responsive design for both desktop and mobile
- Framer Motion animations for smooth transitions
- Dark mode support with theme-aware styling
- Accessible from multiple entry points in the application

### Account Button
- Single entry point in the header for authentication
- Opens the AuthModal with the appropriate initial mode
- Replaces separate login/signup buttons for a cleaner UI

### User Profile Dropdown
- Appears when user is authenticated
- Provides access to dashboard, profile, and logout
- Displays user's name or email

### Protected Routes
- Routes that require authentication redirect to login
- Loading states while checking authentication status
- Redirect to original destination after successful login

## Authentication Flow

### Registration Process
1. User enters full name, email, and password
2. Client-side validation of form inputs
3. Submission to Supabase Auth
4. Email verification sent to user
5. User confirms email to activate account
6. Automatic profile creation in database

### Login Process
1. User enters email and password or chooses alternative method
2. Authentication with Supabase Auth
3. JWT token stored securely
4. User redirected to dashboard or original destination

### Magic Link Process
1. User enters email address
2. Magic link sent to email
3. User clicks link in email
4. Token verification and automatic login
5. Session creation and redirect to dashboard

### Google Authentication Process
1. User clicks "Continue with Google" button
2. Redirected to Google consent screen
3. User authorizes the application
4. Redirected back to application with OAuth token
5. Token exchanged for session
6. Profile created or updated with Google information

### Logout Process
1. User clicks logout button
2. Session terminated in Supabase Auth
3. Local state cleared
4. User redirected to home page

## User Profile Management

### Profile Page
- View and edit personal information
- Upload and manage profile picture
- Update contact information
- View account status

### Profile Data Structure
```typescript
interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  phone_number?: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
  updated_at: string;
  has_completed_onboarding?: boolean;
}
```

### Avatar Management
- Upload profile pictures
- Store in Supabase Storage
- Generate and cache public URLs
- Image optimization and validation

## Security Considerations

### JWT Authentication
- Secure token-based authentication
- Short-lived access tokens
- Token refresh mechanism
- Proper token storage in HTTP-only cookies

### Password Security
- Minimum password strength requirements
- Secure password reset flow
- No plaintext password storage
- Bcrypt hashing handled by Supabase Auth

### Row-Level Security
- Database access restricted by user ID
- Policies enforced at the database level
- Prevents unauthorized data access
- Automatic filtering of queries by user ID

## Technical Implementation

### Frontend Authentication Context
The `AuthContext` provides authentication state and methods throughout the application:

```typescript
interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData?: { full_name?: string }) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  refreshProfile: () => Promise<void>;
  isAuthenticated: boolean;
  checkUserExists: (email: string) => Promise<{ exists: boolean; error: any }>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  signInWithMagicLink: (email: string) => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ data: any, error: any }>;
}
```

### Protected Route Component
The `ProtectedRoute` component ensures that only authenticated users can access certain routes:

```typescript
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireOnboarding = false 
}) => {
  const { isAuthenticated, isLoading, profile } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requireOnboarding && profile && !profile.has_completed_onboarding) {
    return <Navigate to="/onboarding" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
```

### Supabase Integration
Supabase provides the authentication backend with:

- User management
- Session handling
- Email templates
- OAuth providers
- Row-level security

### Database Triggers
Automatic profile creation on user registration:

```sql
CREATE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, created_at, updated_at)
  VALUES (new.id, new.email, now(), now());
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

## Conclusion

The authentication and user management system in AjiraPro provides a secure, flexible, and user-friendly experience. It leverages Supabase Auth for backend security while implementing a modern UI with smooth transitions and responsive design. The system supports multiple authentication methods and integrates seamlessly with the rest of the application.
