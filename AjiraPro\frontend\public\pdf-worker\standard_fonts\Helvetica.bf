%!PS-AdobeFont-1.0: Helvetica 003.002
%%Title: Helvetica
%Version: 003.002
%%CreationDate: Mon Jul 13 16:17:00 2009
%%Creator: Adobe Systems Incorporated
%%BBox: -166 -225 1000 931
%%EndComments

/FontName /Helvetica def
/ItalicAngle 0 def
/isFixedPitch false def
/UnderlinePosition -100 def
/UnderlineThickness 50 def

/CharStrings 228 dict dup begin
/.notdef { 0 0 hsbw endchar } readonly def
/space { 278 0 hsbw endchar } readonly def
/exclam { 278 0 hsbw endchar } readonly def
/quotedbl { 355 0 hsbw endchar } readonly def
/numbersign { 556 0 hsbw endchar } readonly def
/dollar { 556 0 hsbw endchar } readonly def
/percent { 889 0 hsbw endchar } readonly def
/ampersand { 667 0 hsbw endchar } readonly def
/quotesingle { 191 0 hsbw endchar } readonly def
/parenleft { 333 0 hsbw endchar } readonly def
/parenright { 333 0 hsbw endchar } readonly def
/asterisk { 389 0 hsbw endchar } readonly def
/plus { 584 0 hsbw endchar } readonly def
/comma { 278 0 hsbw endchar } readonly def
/hyphen { 333 0 hsbw endchar } readonly def
/period { 278 0 hsbw endchar } readonly def
/slash { 278 0 hsbw endchar } readonly def
/zero { 556 0 hsbw endchar } readonly def
/one { 556 0 hsbw endchar } readonly def
/two { 556 0 hsbw endchar } readonly def
/three { 556 0 hsbw endchar } readonly def
/four { 556 0 hsbw endchar } readonly def
/five { 556 0 hsbw endchar } readonly def
/six { 556 0 hsbw endchar } readonly def
/seven { 556 0 hsbw endchar } readonly def
/eight { 556 0 hsbw endchar } readonly def
/nine { 556 0 hsbw endchar } readonly def
/colon { 278 0 hsbw endchar } readonly def
/semicolon { 278 0 hsbw endchar } readonly def
/less { 584 0 hsbw endchar } readonly def
/equal { 584 0 hsbw endchar } readonly def
/greater { 584 0 hsbw endchar } readonly def
/question { 556 0 hsbw endchar } readonly def
/at { 1015 0 hsbw endchar } readonly def
/A { 667 0 hsbw endchar } readonly def
/B { 667 0 hsbw endchar } readonly def
/C { 722 0 hsbw endchar } readonly def
/D { 722 0 hsbw endchar } readonly def
/E { 667 0 hsbw endchar } readonly def
/F { 611 0 hsbw endchar } readonly def
/G { 778 0 hsbw endchar } readonly def
/H { 722 0 hsbw endchar } readonly def
/I { 278 0 hsbw endchar } readonly def
/J { 500 0 hsbw endchar } readonly def
/K { 667 0 hsbw endchar } readonly def
/L { 556 0 hsbw endchar } readonly def
/M { 833 0 hsbw endchar } readonly def
/N { 722 0 hsbw endchar } readonly def
/O { 778 0 hsbw endchar } readonly def
/P { 667 0 hsbw endchar } readonly def
/Q { 778 0 hsbw endchar } readonly def
/R { 722 0 hsbw endchar } readonly def
/S { 667 0 hsbw endchar } readonly def
/T { 611 0 hsbw endchar } readonly def
/U { 722 0 hsbw endchar } readonly def
/V { 667 0 hsbw endchar } readonly def
/W { 944 0 hsbw endchar } readonly def
/X { 667 0 hsbw endchar } readonly def
/Y { 667 0 hsbw endchar } readonly def
/Z { 611 0 hsbw endchar } readonly def
/bracketleft { 278 0 hsbw endchar } readonly def
/backslash { 278 0 hsbw endchar } readonly def
/bracketright { 278 0 hsbw endchar } readonly def
/asciicircum { 469 0 hsbw endchar } readonly def
/underscore { 556 0 hsbw endchar } readonly def
/grave { 333 0 hsbw endchar } readonly def
/a { 556 0 hsbw endchar } readonly def
/b { 556 0 hsbw endchar } readonly def
/c { 500 0 hsbw endchar } readonly def
/d { 556 0 hsbw endchar } readonly def
/e { 556 0 hsbw endchar } readonly def
/f { 278 0 hsbw endchar } readonly def
/g { 556 0 hsbw endchar } readonly def
/h { 556 0 hsbw endchar } readonly def
/i { 222 0 hsbw endchar } readonly def
/j { 222 0 hsbw endchar } readonly def
/k { 500 0 hsbw endchar } readonly def
/l { 222 0 hsbw endchar } readonly def
/m { 833 0 hsbw endchar } readonly def
/n { 556 0 hsbw endchar } readonly def
/o { 556 0 hsbw endchar } readonly def
/p { 556 0 hsbw endchar } readonly def
/q { 556 0 hsbw endchar } readonly def
/r { 333 0 hsbw endchar } readonly def
/s { 500 0 hsbw endchar } readonly def
/t { 278 0 hsbw endchar } readonly def
/u { 556 0 hsbw endchar } readonly def
/v { 500 0 hsbw endchar } readonly def
/w { 722 0 hsbw endchar } readonly def
/x { 500 0 hsbw endchar } readonly def
/y { 500 0 hsbw endchar } readonly def
/z { 500 0 hsbw endchar } readonly def
/braceleft { 334 0 hsbw endchar } readonly def
/bar { 260 0 hsbw endchar } readonly def
/braceright { 334 0 hsbw endchar } readonly def
/asciitilde { 584 0 hsbw endchar } readonly def
end readonly def
