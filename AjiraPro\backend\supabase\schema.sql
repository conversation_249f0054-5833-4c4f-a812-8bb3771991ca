-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Create resumes table
CREATE TABLE IF NOT EXISTS resumes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) NOT NULL,
  title TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending_payment',
  file_path TEXT,
  ats_score INTEGER,
  -- Enhanced scoring fields
  overall_score DECIMAL(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  claude_score DECIMAL(5,2) CHECK (claude_score >= 0 AND claude_score <= 100),
  openai_score DECIMAL(5,2) CHECK (openai_score >= 0 AND openai_score <= 100),
  career_overview_score DECIMAL(5,2) CHECK (career_overview_score >= 0 AND career_overview_score <= 100),
  experience_score DECIMAL(5,2) CHECK (experience_score >= 0 AND experience_score <= 100),
  education_score DECIMAL(5,2) CHECK (education_score >= 0 AND education_score <= 100),
  additional_qualifications_score DECIMAL(5,2) CHECK (additional_qualifications_score >= 0 AND additional_qualifications_score <= 100),
  content_quality_score DECIMAL(5,2) CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
  scoring_feedback JSONB,
  last_scored_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- CV table removed

-- Create scoring_history table
CREATE TABLE IF NOT EXISTS scoring_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  resume_id UUID REFERENCES resumes(id) ON DELETE CASCADE NOT NULL,
  overall_score DECIMAL(5,2) CHECK (overall_score >= 0 AND overall_score <= 100),
  claude_score DECIMAL(5,2) CHECK (claude_score >= 0 AND claude_score <= 100),
  openai_score DECIMAL(5,2) CHECK (openai_score >= 0 AND openai_score <= 100),
  career_overview_score DECIMAL(5,2) CHECK (career_overview_score >= 0 AND career_overview_score <= 100),
  experience_score DECIMAL(5,2) CHECK (experience_score >= 0 AND experience_score <= 100),
  education_score DECIMAL(5,2) CHECK (education_score >= 0 AND education_score <= 100),
  additional_qualifications_score DECIMAL(5,2) CHECK (additional_qualifications_score >= 0 AND additional_qualifications_score <= 100),
  content_quality_score DECIMAL(5,2) CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
  scoring_feedback JSONB,
  scoring_version TEXT DEFAULT '1.0',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Create drafts_resume table
CREATE TABLE IF NOT EXISTS drafts_resume (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  resume_id UUID REFERENCES resumes(id) NOT NULL,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Drafts CV table removed

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) NOT NULL,
  resume_id UUID REFERENCES resumes(id) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'KES',
  status TEXT NOT NULL DEFAULT 'pending',
  payment_reference TEXT,
  payment_method TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Create templates_resume table
CREATE TABLE IF NOT EXISTS templates_resume (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  file_path TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Templates CV table removed

-- Create job_descriptions table
CREATE TABLE IF NOT EXISTS job_descriptions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  resume_id UUID REFERENCES resumes(id) NOT NULL,
  content TEXT NOT NULL,
  url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Set up Row Level Security (RLS)
-- Profiles: Users can only access their own profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Resumes: Users can only access their own resumes
ALTER TABLE resumes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own resumes" ON resumes
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own resumes" ON resumes
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own resumes" ON resumes
  FOR UPDATE USING (auth.uid() = user_id);

-- CV RLS policies removed

-- Drafts Resume: Users can only access drafts for their own resumes
ALTER TABLE drafts_resume ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view drafts for their own resumes" ON drafts_resume
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = drafts_resume.resume_id
    )
  );
CREATE POLICY "Users can insert drafts for their own resumes" ON drafts_resume
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = drafts_resume.resume_id
    )
  );

-- Drafts CV RLS policies removed

-- Payments: Users can only access their own payments
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own payments" ON payments
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own payments" ON payments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Templates: All users can view templates
ALTER TABLE templates_resume ENABLE ROW LEVEL SECURITY;
CREATE POLICY "All users can view resume templates" ON templates_resume
  FOR SELECT USING (true);

-- CV templates RLS policies removed

-- Job Descriptions: Users can only access job descriptions for their own resumes/CVs
ALTER TABLE job_descriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view job descriptions for their own resumes" ON job_descriptions
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = job_descriptions.resume_id
    )
  );
CREATE POLICY "Users can insert job descriptions for their own resumes" ON job_descriptions
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = job_descriptions.resume_id
    )
  );

-- Scoring History: Users can only access scoring history for their own resumes
ALTER TABLE scoring_history ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view scoring history for their own resumes" ON scoring_history
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = scoring_history.resume_id
    )
  );
CREATE POLICY "Users can insert scoring history for their own resumes" ON scoring_history
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM resumes WHERE id = scoring_history.resume_id
    )
  );

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile when user signs up
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update resume status when payment is confirmed
CREATE OR REPLACE FUNCTION public.update_document_status_on_payment()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'paid' THEN
    UPDATE public.resumes
    SET status = 'processing'
    WHERE id = NEW.resume_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update resume status when payment is confirmed
CREATE TRIGGER on_payment_status_change
  AFTER UPDATE ON public.payments
  FOR EACH ROW
  WHEN (OLD.status <> 'paid' AND NEW.status = 'paid')
  EXECUTE FUNCTION public.update_document_status_on_payment();

-- Create function to auto-delete expired drafts (older than 24 hours)
CREATE OR REPLACE FUNCTION public.delete_expired_drafts()
RETURNS void AS $$
BEGIN
  DELETE FROM public.drafts_resume
  WHERE created_at < NOW() - INTERVAL '24 hours'
  AND resume_id IN (SELECT id FROM public.resumes WHERE status = 'pending_payment');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a cron job to run the delete_expired_drafts function daily
-- Note: This requires the pg_cron extension to be enabled
-- SELECT cron.schedule('0 0 * * *', 'SELECT public.delete_expired_drafts()');
