AjiraPro Features
Core Features
Feature	Description
Resume Tailoring	AI generates resumes optimized for specific job descriptions (text or URL input)
ATS Compliance Check	Scores resumes and CVs for ATS compatibility, shows why it's not ATS friendly, and suggests improvements
Step-by-Step Builder	Guided questions for users creating cv and resumes from scratch
CV Revamp	Adding information to the existing CVs and Improving them for modern standards (upload PDF/Word)
Secure Payments	One-time payment via Flutterwave to unlock downloads
Advanced Features
Multi-Template Support
•	5+ ATS-friendly templates (Modern, Classic, Minimalist)
•	Custom styling options while maintaining ATS compatibility
Cover Letter Generator
•	AI creates job-specific cover letters
•	Matches tone and formatting of the generated Cv/resume.
User Experience Features
•	Progress tracking (progressbar, spinning image with progress, etc)
•	Responsive design for all devices
•	Pre-payment value demonstration (ATS score previews, keyword analysis, improvement suggestions)
•	Interactive visual elements showing document analysis and optimization potential
•	Persuasive call-to-action design with benefit-focused messaging
•	Social proof and success metrics to build user confidence
Feature User Flow
1.	User signs up → System stores credentials via Supabase Auth
2.	User selects Resume/CV creation or revamp → UI directs to input form/questionnaire prompter or upload
3.	User completes form or uploads document → Data sent to FastAPI backend
4.	Payment modal appears → User completes payment via Flutterwave
5.	Webhook confirms payment → Celery task triggers AI generation
6.	Document generated (.docx), converted to .pdf → Stored in Supabase Storage
7.	User notified with download link (signed URL) Also show “Email Me” option.
