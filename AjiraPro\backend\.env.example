# Supabase Configuration
SUPABASE_URL=
SUPABASE_KEY=

# AI Service Configuration
OPENAI_API_KEY=
CLAUDE_API_KEY=

# AI Service Settings
OPENAI_MODEL=gpt-4
CLAUDE_MODEL=claude-3-sonnet-20240229
AI_REQUEST_TIMEOUT=60
AI_MAX_RETRIES=3

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Flutterwave Configuration
FLUTTERWAVE_PUBLIC_KEY=your_flutterwave_public_key
FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret_key
FLUTTERWAVE_ENCRYPTION_KEY=your_flutterwave_encryption_key

# Application Configuration
DEBUG=True
ENVIRONMENT=development
