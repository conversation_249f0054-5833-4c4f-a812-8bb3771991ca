import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const ExpiredToken: React.FC = () => {
  const [errorMessage, setErrorMessage] = useState<string>('Your link has expired or is invalid.');
  const [linkType, setLinkType] = useState<'magic' | 'reset'>('magic');
  const location = useLocation();

  useEffect(() => {
    // Parse the URL to extract error information
    const searchParams = new URLSearchParams(location.search);
    const errorCode = searchParams.get('error_code');
    const errorDesc = searchParams.get('error_description');
    const errorType = searchParams.get('error_type') || '';

    // Determine if this is a magic link or reset password link
    if (errorType === 'recovery' ||
        (errorDesc && errorDesc.toLowerCase().includes('reset')) ||
        location.pathname.includes('reset-password')) {
      setLinkType('reset');
    } else {
      setLinkType('magic');
    }

    if (errorCode === 'otp_expired') {
      if (linkType === 'reset') {
        setErrorMessage('The password reset link you followed has expired. Please request a new one.');
      } else {
        setErrorMessage('The magic link you followed has expired. Please request a new one.');
      }
    } else if (errorCode === 'access_denied') {
      setErrorMessage('Access denied. The link you followed is invalid or has expired.');
    } else if (errorDesc) {
      // Use the error description if available
      try {
        const decodedDesc = decodeURIComponent(errorDesc.replace(/\+/g, ' '));
        setErrorMessage(decodedDesc);
      } catch (e) {
        // If decoding fails, use the raw error description
        setErrorMessage(errorDesc);
      }
    }

    // Clean up the URL to prevent infinite redirects
    if (location.search || location.hash) {
      // Replace the current URL with a clean version without query parameters or hash
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location, linkType]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Link Expired
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{errorMessage}</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Link
                to="/login"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Back to Sign In
              </Link>
            </div>

            {linkType === 'reset' ? (
              <div>
                <Link
                  to="/forgot-password"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Request New Password Reset
                </Link>
              </div>
            ) : (
              <div>
                <Link
                  to="/login?magic=true"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Request New Magic Link
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpiredToken;
