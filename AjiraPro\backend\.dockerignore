# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local development
.DS_Store
.coverage
htmlcov/
.pytest_cache/

# Git
.git/
.gitignore

# Docker
.dockerignore
Dockerfile

# Node.js (if any)
node_modules/
package-lock.json
