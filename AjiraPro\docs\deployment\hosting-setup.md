# AjiraPro Hosting Setup

This document outlines the hosting infrastructure for AjiraPro and provides instructions for deployment.

## Frontend Hosting (Cloudflare Pages)

### Prerequisites
- GitHub repository with your frontend code
- Cloudflare account

### Deployment Steps

1. **Push your code to GitHub**:
   ```bash
   cd frontend
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin <your-github-repo-url>
   git push -u origin main
   ```

2. **Set up Cloudflare Pages**:
   - Log in to your Cloudflare dashboard
   - Go to Pages > Create a project
   - Connect to your GitHub account and select your repository
   - Configure build settings:
     - Framework preset: Create React App
     - Build command: `npm run build`
     - Build output directory: `build`
     - Root directory: (leave blank or specify `frontend` if your repo includes both frontend and backend)
   - Add environment variables if needed
   - Click "Save and Deploy"

3. **Configure Custom Domain** (optional):
   - In your Cloudflare Pages project, go to "Custom domains"
   - Add your domain (e.g., ajirapro.com)
   - Follow the instructions to set up DNS records

## Backend Hosting (Railway)

### Prerequisites
- GitHub repository with your backend code
- Railway account

### Deployment Steps

1. **Push your code to GitHub**:
   ```bash
   cd backend
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin <your-github-repo-url>
   git push -u origin main
   ```

2. **Set up Railway Project**:
   - Log in to Railway dashboard
   - Create a new project
   - Select "Deploy from GitHub repo"
   - Connect to your GitHub account and select your repository
   - Configure environment variables:
     - `SUPABASE_URL`: Your Supabase URL
     - `SUPABASE_KEY`: Your Supabase service role key (not anon key for backend)
     - `OPENAI_API_KEY`: Your OpenAI API key
     - `ENVIRONMENT`: `production`
     - `DEBUG`: `False`
     - Any other required environment variables

3. **Add Redis Service** (optional):
   - In your Railway project, click "New"
   - Select "Redis"
   - Once provisioned, link it to your backend service
   - Railway will automatically add the `REDIS_URL` environment variable

4. **Configure Custom Domain** (optional):
   - In your Railway project, go to "Settings" > "Domains"
   - Add your domain (e.g., api.ajirapro.com)
   - Follow the instructions to set up DNS records

## Continuous Deployment

Both Cloudflare Pages and Railway support continuous deployment:

- When you push changes to your GitHub repository, they will automatically be deployed
- You can configure specific branches for staging and production environments

## Local Development with Production Services

To develop locally while using production services:

1. **Frontend**:
   ```bash
   cd frontend
   REACT_APP_API_URL=https://api.ajirapro.com npm start
   ```

2. **Backend**:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   ```

## Troubleshooting

### Cloudflare Pages Issues
- Check build logs for any errors
- Ensure all dependencies are properly listed in package.json
- Verify that environment variables are correctly set

### Railway Issues
- Check logs for any errors
- Ensure all dependencies are listed in requirements.txt
- Verify that environment variables are correctly set
- Check that the Procfile or railway.json configuration is correct
