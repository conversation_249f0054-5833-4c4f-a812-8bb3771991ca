import React, { useEffect, useState, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface ResetPasswordRedirectProps {
  fallback?: ReactNode;
}

const ResetPasswordRedirect: React.FC<ResetPasswordRedirectProps> = ({ fallback }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [shouldRedirect, setShouldRedirect] = useState<boolean>(true);

  useEffect(() => {
    // Check if we're in a password reset flow
    const searchParams = new URLSearchParams(location.search);
    const pathParam = searchParams.get('p');
    const hash = location.hash;

    // Check for error parameters that indicate an expired token
    const errorCode = searchParams.get('error_code');
    const errorDescription = searchParams.get('error_description');
    const hashErrorCode = hash && hash.includes('error_code=') ?
      new URLSearchParams(hash.substring(1)).get('error_code') : null;

    if (errorCode === 'otp_expired' ||
        hashErrorCode === 'otp_expired' ||
        (errorDescription && errorDescription.includes('expired')) ||
        (hash && hash.includes('error=access_denied'))) {
      // This is an expired token, redirect to the expired token page
      let queryParams = '';

      // Combine error information from both search params and hash
      if (errorCode || errorDescription) {
        queryParams = `?error_code=${errorCode || ''}&error_description=${errorDescription || ''}`;
      } else if (hash && hash.includes('error_code=')) {
        // Extract error information from hash
        const hashParams = new URLSearchParams(hash.substring(1));
        const hashErrorCode = hashParams.get('error_code') || '';
        const hashErrorDesc = hashParams.get('error_description') || '';
        queryParams = `?error_code=${hashErrorCode}&error_description=${hashErrorDesc}`;
      }

      navigate(`/auth/token-expired${queryParams}`, { replace: true });
    }
    // Check if this is a magic link or reset password flow
    else if (pathParam && pathParam === '/reset-password') {
      // This is a reset password flow
      // If we have a path parameter but no hash, we need to add the hash from the path
      if (!hash) {
        // Check if we have any other query parameters that might contain the token
        const token = searchParams.get('token');
        const type = searchParams.get('type');

        if (token && type) {
          // Construct a hash with the token and type
          const newHash = `#access_token=${token}&type=${type}`;
          navigate('/reset-password' + newHash, { replace: true });
        } else {
          // Just redirect to reset-password and hope for the best
          navigate('/reset-password', { replace: true });
        }
      } else {
        // We have a hash, preserve it
        navigate('/reset-password' + hash, { replace: true });
      }
    } else if (pathParam && pathParam === '/auth/magic-link') {
      // This is a magic link flow with a path parameter
      if (hash) {
        // Preserve the hash
        navigate('/auth/magic-link' + hash, { replace: true });
      } else {
        navigate('/auth/magic-link', { replace: true });
      }
    } else if (hash && hash.includes('type=recovery')) {
      // This is a password reset flow with a recovery token
      navigate('/reset-password' + hash, { replace: true });
    } else if (hash && hash.includes('access_token') && !hash.includes('type=recovery')) {
      // This is a magic link flow (has access_token but not recovery type)
      // Redirect to the magic link success page
      navigate('/auth/magic-link' + hash, { replace: true });
    } else {
      // Not a reset password or magic link flow, render the fallback
      setShouldRedirect(false);
    }
  }, [navigate, location]);

  // If we're still checking or have redirected, don't render anything
  // Otherwise, render the fallback component
  return shouldRedirect ? null : <>{fallback}</>;
};

export default ResetPasswordRedirect;
