name: Frontend CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'AjiraPro/frontend/**'
      - '.github/workflows/frontend-ci-cd.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'AjiraPro/frontend/**'
      - '.github/workflows/frontend-ci-cd.yml'

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: AjiraPro/frontend

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: AjiraPro/frontend/package-lock.json

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Run linting
        run: npm run lint || echo "Linting not configured"

      - name: Run tests
        run: echo "Skipping tests for now"

      - name: Build
        run: npm run build

      - name: Verify build
        run: |
          echo "Build completed successfully!"
          echo "Deployment will be handled automatically by Cloudflare Pages GitHub integration."
