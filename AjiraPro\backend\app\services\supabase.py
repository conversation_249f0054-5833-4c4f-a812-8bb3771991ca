from supabase import create_client, Client
from app.core.config import settings
import os
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Debug environment variables
supabase_url = settings.SUPABASE_URL
supabase_key = settings.SUPABASE_KEY

logger.info(f"SUPABASE_URL: {'Set' if supabase_url else 'Not set'}")
logger.info(f"SUPABASE_KEY: {'Set' if supabase_key else 'Not set'}")

# Fallback to environment variables directly if settings don't work
if not supabase_url:
    supabase_url = os.environ.get("SUPABASE_URL", "")
    logger.info(f"Fallback SUPABASE_URL: {'Set' if supabase_url else 'Not set'}")

if not supabase_key:
    supabase_key = os.environ.get("SUPABASE_KEY", "")
    logger.info(f"Fallback SUPABASE_KEY: {'Set' if supabase_key else 'Not set'}")

# Create a single supabase client for interacting with your database
try:
    if not supabase_url:
        raise ValueError("SUPABASE_URL is not set")
    if not supabase_key:
        raise ValueError("SUPABASE_KEY is not set")

    logger.info("Creating Supabase client...")
    supabase: Client = create_client(supabase_url, supabase_key)
    logger.info("Supabase client created successfully")
except Exception as e:
    logger.error(f"Failed to create Supabase client: {str(e)}")
    # Create a dummy client for development/testing
    # In production, this will still fail when methods are called
    supabase = None

# User profile functions
async def get_profile(user_id: str):
    """Get a user profile by ID"""
    response = supabase.table("profiles").select("*").eq("id", user_id).single().execute()
    return response

async def update_profile(user_id: str, updates: dict):
    """Update a user profile"""
    response = supabase.table("profiles").update(updates).eq("id", user_id).execute()
    return response

# Resume functions
async def create_resume(user_id: str, title: str):
    """Create a new resume"""
    response = supabase.table("resumes").insert({
        "user_id": user_id,
        "title": title,
        "status": "pending_payment"
    }).execute()
    return response

async def get_resume(resume_id: str):
    """Get a resume by ID"""
    response = supabase.table("resumes").select("*").eq("id", resume_id).single().execute()
    return response

async def update_resume(resume_id: str, updates: dict):
    """Update a resume"""
    response = supabase.table("resumes").update(updates).eq("id", resume_id).execute()
    return response

async def get_user_resumes(user_id: str):
    """Get all resumes for a user"""
    response = supabase.table("resumes").select("*").eq("user_id", user_id).execute()
    return response

# CV functions
async def create_cv(user_id: str, title: str):
    """Create a new CV"""
    response = supabase.table("cvs").insert({
        "user_id": user_id,
        "title": title,
        "status": "pending_payment"
    }).execute()
    return response

async def get_cv(cv_id: str):
    """Get a CV by ID"""
    response = supabase.table("cvs").select("*").eq("id", cv_id).single().execute()
    return response

async def update_cv(cv_id: str, updates: dict):
    """Update a CV"""
    response = supabase.table("cvs").update(updates).eq("id", cv_id).execute()
    return response

async def get_user_cvs(user_id: str):
    """Get all CVs for a user"""
    response = supabase.table("cvs").select("*").eq("user_id", user_id).execute()
    return response

# Draft functions
async def create_resume_draft(resume_id: str, content: dict):
    """Create a new resume draft"""
    response = supabase.table("drafts_resume").insert({
        "resume_id": resume_id,
        "content": content
    }).execute()
    return response

async def get_resume_draft(resume_id: str):
    """Get the latest draft for a resume"""
    response = supabase.table("drafts_resume").select("*").eq("resume_id", resume_id).order("created_at", desc=True).limit(1).execute()
    return response

async def create_cv_draft(cv_id: str, content: dict):
    """Create a new CV draft"""
    response = supabase.table("drafts_cv").insert({
        "cv_id": cv_id,
        "content": content
    }).execute()
    return response

async def get_cv_draft(cv_id: str):
    """Get the latest draft for a CV"""
    response = supabase.table("drafts_cv").select("*").eq("cv_id", cv_id).order("created_at", desc=True).limit(1).execute()
    return response

# Payment functions
async def create_payment(user_id: str, amount: float, resume_id: str = None, cv_id: str = None):
    """Create a new payment record"""
    payment_data = {
        "user_id": user_id,
        "amount": amount,
        "status": "pending"
    }

    if resume_id:
        payment_data["resume_id"] = resume_id
    elif cv_id:
        payment_data["cv_id"] = cv_id

    response = supabase.table("payments").insert(payment_data).execute()
    return response

async def update_payment(payment_id: str, updates: dict):
    """Update a payment record"""
    response = supabase.table("payments").update(updates).eq("id", payment_id).execute()
    return response

async def get_payment(payment_id: str):
    """Get a payment by ID"""
    response = supabase.table("payments").select("*").eq("id", payment_id).single().execute()
    return response

# Template functions
async def get_resume_templates():
    """Get all resume templates"""
    response = supabase.table("templates_resume").select("*").execute()
    return response

async def get_cv_templates():
    """Get all CV templates"""
    response = supabase.table("templates_cv").select("*").execute()
    return response

# Job description functions
async def create_job_description(content: str, url: str = None, resume_id: str = None, cv_id: str = None):
    """Create a new job description"""
    job_data = {
        "content": content,
        "url": url
    }

    if resume_id:
        job_data["resume_id"] = resume_id
    elif cv_id:
        job_data["cv_id"] = cv_id

    response = supabase.table("job_descriptions").insert(job_data).execute()
    return response

async def get_job_description(job_id: str):
    """Get a job description by ID"""
    response = supabase.table("job_descriptions").select("*").eq("id", job_id).single().execute()
    return response

# Storage functions
def get_resume_download_url(file_path: str):
    """Get a signed URL for downloading a resume"""
    response = supabase.storage.from_("resumes").create_signed_url(file_path, 60 * 60)  # 1 hour expiry
    return response

def get_cv_download_url(file_path: str):
    """Get a signed URL for downloading a CV"""
    response = supabase.storage.from_("cvs").create_signed_url(file_path, 60 * 60)  # 1 hour expiry
    return response

def get_template_download_url(file_path: str):
    """Get a URL for downloading a template"""
    response = supabase.storage.from_("templates").get_public_url(file_path)
    return response
# 1 hour expiry above