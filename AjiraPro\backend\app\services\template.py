from fastapi import Depends
from typing import List, Optional
from ..models.template import TemplateResponse
from .supabase import supabase

class TemplateService:
    async def get_resume_templates(self, category: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[TemplateResponse]:
        """
        Get all resume templates, optionally filtered by category.
        """
        query = supabase.table("templates").select("*").eq("type", "resume")
        
        if category:
            query = query.eq("category", category)
        
        response = query.range(skip, skip + limit - 1).execute()
        
        templates = []
        for template_data in response.data:
            templates.append(TemplateResponse(**template_data))
        
        return templates

    async def get_resume_template(self, template_id: str) -> Optional[TemplateResponse]:
        """
        Get a specific resume template by ID.
        """
        response = supabase.table("templates").select("*").eq("id", template_id).eq("type", "resume").execute()
        
        if len(response.data) > 0:
            return TemplateResponse(**response.data[0])
        
        return None

    async def get_cv_templates(self, category: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[TemplateResponse]:
        """
        Get all CV templates, optionally filtered by category.
        """
        query = supabase.table("templates").select("*").eq("type", "cv")
        
        if category:
            query = query.eq("category", category)
        
        response = query.range(skip, skip + limit - 1).execute()
        
        templates = []
        for template_data in response.data:
            templates.append(TemplateResponse(**template_data))
        
        return templates

    async def get_cv_template(self, template_id: str) -> Optional[TemplateResponse]:
        """
        Get a specific CV template by ID.
        """
        response = supabase.table("templates").select("*").eq("id", template_id).eq("type", "cv").execute()
        
        if len(response.data) > 0:
            return TemplateResponse(**response.data[0])
        
        return None
