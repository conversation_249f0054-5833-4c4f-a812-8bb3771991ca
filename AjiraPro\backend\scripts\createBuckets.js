// createBuckets.js
const path = require("path");
require("dotenv").config({ path: path.resolve(__dirname, "../.env") });
const { createClient } = require("@supabase/supabase-js");

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(
    "Error: SUPABASE_URL and SUPABASE_KEY must be set in the .env file"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

const createBuckets = async () => {
  try {
    console.log("Creating storage buckets...");

    // Create resumes bucket
    console.log("Creating resumes bucket...");
    const { data: resumesBucket, error: resumesError } =
      await supabase.storage.createBucket("resumes", {
        public: false,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: [
          "application/pdf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
      });

    if (resumesError) {
      console.error("Error creating resumes bucket:", resumesError.message);
    } else {
      console.log("✅ Resumes bucket created successfully");
    }

    // Create cvs bucket
    console.log("Creating cvs bucket...");
    const { data: cvsBucket, error: cvsError } =
      await supabase.storage.createBucket("cvs", {
        public: false,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: [
          "application/pdf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
      });

    if (cvsError) {
      console.error("Error creating cvs bucket:", cvsError.message);
    } else {
      console.log("✅ CVs bucket created successfully");
    }

    // Create templates bucket
    console.log("Creating templates bucket...");
    const { data: templatesBucket, error: templatesError } =
      await supabase.storage.createBucket("templates", {
        public: true, // Templates are public for easy access
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: [
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
      });

    if (templatesError) {
      console.error("Error creating templates bucket:", templatesError.message);
    } else {
      console.log("✅ Templates bucket created successfully");
    }

    // Create avatars bucket for user profile pictures
    console.log("Creating avatars bucket...");
    const { data: avatarsBucket, error: avatarsError } =
      await supabase.storage.createBucket("avatars", {
        public: true, // Avatars are public for easy access
        fileSizeLimit: 2097152, // 2MB
        allowedMimeTypes: ["image/png", "image/jpeg", "image/gif"],
      });

    if (avatarsError) {
      console.error("Error creating avatars bucket:", avatarsError.message);
    } else {
      console.log("✅ Avatars bucket created successfully");
    }

    console.log("Storage bucket creation completed!");
  } catch (error) {
    console.error("Unexpected error:", error.message);
  }
};

// Call the function
createBuckets()
  .then(() => console.log("Script execution completed"))
  .catch((err) => console.error("Script execution failed:", err));
