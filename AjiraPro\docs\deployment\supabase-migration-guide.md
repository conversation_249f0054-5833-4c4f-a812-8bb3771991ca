# Supabase Database Migration Guide

## Overview
This guide walks you through updating your Supabase database to support the new AI-powered resume parsing and scoring system. You'll be replacing the existing dummy scoring system with a comprehensive two-stage workflow:

1. **Parsing Stage (Claude)**: Extract structured content from resume files
2. **Scoring Stage (OpenAI + Claude)**: Analyze parsed content and generate scores

## Prerequisites
- Access to your Supabase dashboard
- Admin privileges on your Supabase project
- Backup of existing data (recommended)

## Migration Steps

### Step 1: Access Supabase SQL Editor
1. Go to your Supabase dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **New Query** to create a new SQL script

### Step 2: Run Database Migration Commands

Copy and paste the commands from `backend/supabase/supabase_dashboard_commands.sql` **one section at a time** in the following order:

#### 2.1 Add Parsing and Scoring Columns to Resumes Table
```sql
-- STEP 1: Add new parsing and scoring columns to existing resumes table
ALTER TABLE resumes
-- Resume parsing fields (Claude)
ADD COLUMN IF NOT EXISTS parsed_content JSONB,
ADD COLUMN IF NOT EXISTS parsing_status TEXT DEFAULT 'pending',
-- ... (copy the complete STEP 1 from the file)
```

**Expected Result:** ✅ Success - 16 new columns added to resumes table (4 parsing + 12 scoring)

#### 2.2 Create Scoring History Table
```sql
-- STEP 2: Create scoring_history table
CREATE TABLE IF NOT EXISTS scoring_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  -- ... (copy the complete STEP 2 from the file)
```

**Expected Result:** ✅ Success - New table `scoring_history` created

#### 2.3 Add RLS Policies
```sql
-- STEP 3: Add RLS policies for scoring_history table
ALTER TABLE scoring_history ENABLE ROW LEVEL SECURITY;
-- ... (copy the complete STEP 3 from the file)
```

**Expected Result:** ✅ Success - RLS enabled and policies created

#### 2.4 Create Performance Indexes
```sql
-- STEP 4: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scoring_history_resume_id ON scoring_history(resume_id);
-- ... (copy the complete STEP 4 from the file)
```

**Expected Result:** ✅ Success - 4 indexes created

#### 2.5 Create Scoring History Function
```sql
-- STEP 5: Create function to automatically save scoring history
CREATE OR REPLACE FUNCTION public.save_scoring_history()
-- ... (copy the complete STEP 5 from the file)
```

**Expected Result:** ✅ Success - Function `save_scoring_history` created

#### 2.6 Create Trigger
```sql
-- STEP 6: Create trigger to automatically save scoring history
DROP TRIGGER IF EXISTS on_resume_scoring_update ON public.resumes;
-- ... (copy the complete STEP 6 from the file)
```

**Expected Result:** ✅ Success - Trigger `on_resume_scoring_update` created

#### 2.7 Add Documentation Comments
```sql
-- STEP 7: Add comments for documentation
COMMENT ON TABLE scoring_history IS 'Stores historical scoring data for resumes to track improvements over time';
-- ... (copy the complete STEP 7 from the file)
```

**Expected Result:** ✅ Success - Comments added to tables and columns

### Step 3: Verify Migration Success

Run the verification queries from **STEP 9** in the SQL file:

#### 3.1 Verify Resume Table Columns
```sql
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'resumes'
AND column_name IN (
  'parsed_content', 'parsing_status', 'parsed_at', 'parsing_error',
  'overall_score', 'claude_score', 'openai_score',
  'career_overview_score', 'experience_score', 'education_score',
  'additional_qualifications_score', 'content_quality_score',
  'scoring_feedback', 'scoring_status', 'last_scored_at', 'scoring_error'
);
```

**Expected Result:** Should return 16 rows showing the new parsing and scoring columns

#### 3.2 Verify Scoring History Table
```sql
SELECT table_name, column_name, data_type
FROM information_schema.columns
WHERE table_name = 'scoring_history'
ORDER BY ordinal_position;
```

**Expected Result:** Should return 13 rows showing all scoring_history columns (it did)

#### 3.3 Verify Trigger Creation
```sql
SELECT trigger_name, event_manipulation, event_object_table
FROM information_schema.triggers
WHERE trigger_name = 'on_resume_scoring_update';
```

**Expected Result:** Should return 1 row showing the trigger details

### Step 4: Test the Migration

#### 4.1 Test Scoring History Trigger
```sql
-- Test the trigger by updating a resume score
UPDATE resumes
SET overall_score = 85.5, claude_score = 90.0, openai_score = 84.0, last_scored_at = NOW()
WHERE id = (SELECT id FROM resumes LIMIT 1);

-- Check if history was created
SELECT * FROM scoring_history ORDER BY created_at DESC LIMIT 1;
```

**Expected Result:** Should show a new record in scoring_history

#### 4.2 Test RLS Policies
```sql
-- Verify RLS is working (should only show data for authenticated user)
SELECT COUNT(*) FROM scoring_history;
```

**Expected Result:** Should respect user permissions

## Post-Migration Tasks

### 1. Update Environment Variables
Add these to your Railway deployment and local `.env`:

```env
# AI Service Configuration
CLAUDE_API_KEY=your_claude_api_key_here
OPENAI_API_KEY=your_existing_openai_key

# AI Service Settings (Optional)
OPENAI_MODEL=gpt-4
CLAUDE_MODEL=claude-3-sonnet-20240229
AI_REQUEST_TIMEOUT=60
AI_MAX_RETRIES=3
```

### 2. Deploy Backend Changes
1. Commit and push the backend changes to your repository
2. Railway will automatically deploy the updated backend
3. Verify the health endpoint shows AI services: `/api/health`

### 3. Clean Up Dummy Data (Optional)
If you want to reset existing dummy ATS scores:

```sql
UPDATE resumes SET ats_score = NULL WHERE ats_score IS NOT NULL;
```

## Troubleshooting

### Common Issues

#### Issue: "Column already exists" error
**Solution:** The `IF NOT EXISTS` clause should prevent this, but if it occurs, check if columns were partially added and continue with remaining steps.

#### Issue: "Permission denied" error
**Solution:** Ensure you're using the service role key or have admin privileges in Supabase.

#### Issue: "Trigger already exists" error
**Solution:** The script includes `DROP TRIGGER IF EXISTS` to handle this. If it persists, manually drop the trigger first.

#### Issue: RLS policies not working
**Solution:** Verify that the policies reference the correct table relationships and that auth.uid() is available.

### Rollback Plan (Emergency)

If you need to rollback the changes:

```sql
-- Remove new columns (WARNING: This will delete data)
ALTER TABLE resumes
DROP COLUMN IF EXISTS overall_score,
DROP COLUMN IF EXISTS claude_score,
DROP COLUMN IF EXISTS openai_score,
DROP COLUMN IF EXISTS career_overview_score,
DROP COLUMN IF EXISTS experience_score,
DROP COLUMN IF EXISTS education_score,
DROP COLUMN IF EXISTS additional_qualifications_score,
DROP COLUMN IF EXISTS content_quality_score,
DROP COLUMN IF EXISTS scoring_feedback,
DROP COLUMN IF EXISTS last_scored_at;

-- Drop scoring history table
DROP TABLE IF EXISTS scoring_history CASCADE;

-- Drop function
DROP FUNCTION IF EXISTS public.save_scoring_history();
```

## Verification Checklist

- [ ] All 16 new columns added to resumes table (4 parsing + 12 scoring)
- [ ] scoring_history table created with 15 columns (includes parsing data)
- [ ] RLS policies enabled and working
- [ ] 7 performance indexes created (including parsing indexes)
- [ ] save_scoring_history function created (handles parsing + scoring)
- [ ] on_resume_scoring_update trigger created
- [ ] Comments added to tables and columns
- [ ] Test parsing/scoring update creates history record
- [ ] Environment variables updated (Claude + OpenAI keys)
- [ ] Backend deployed successfully
- [ ] Health endpoint shows AI services configured

## Next Steps

After successful migration:

1. **Phase 2 Implementation**: Begin implementing the core scoring engine
2. **Testing**: Test the new scoring system with sample resumes
3. **Monitoring**: Monitor the health endpoint for AI service status
4. **Documentation**: Update API documentation with new scoring endpoints

The database is now ready for the AI-powered scoring system implementation!
