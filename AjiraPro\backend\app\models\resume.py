from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from decimal import Decimal

class Education(BaseModel):
    institution: str
    degree: str
    field_of_study: Optional[str] = None
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None

class Experience(BaseModel):
    company: str
    position: str
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    achievements: Optional[List[str]] = None

class Skill(BaseModel):
    name: str
    level: Optional[str] = None
    category: Optional[str] = None

class Project(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    url: Optional[str] = None
    technologies: Optional[List[str]] = None

class Language(BaseModel):
    name: str
    proficiency: str

class Certification(BaseModel):
    name: str
    issuer: str
    date: Optional[str] = None
    url: Optional[str] = None
    expiry_date: Optional[str] = None

class ResumeBase(BaseModel):
    title: str
    template_id: Optional[str] = None
    personal_info: Dict[str, Any]
    objective: Optional[str] = None
    education: Optional[List[Education]] = None
    experience: Optional[List[Experience]] = None
    skills: Optional[List[Skill]] = None
    projects: Optional[List[Project]] = None
    languages: Optional[List[Language]] = None
    certifications: Optional[List[Certification]] = None
    references: Optional[List[Dict[str, Any]]] = None
    custom_sections: Optional[Dict[str, Any]] = None

class ResumeCreate(ResumeBase):
    pass

class ResumeUpdate(ResumeBase):
    title: Optional[str] = None
    template_id: Optional[str] = None
    personal_info: Optional[Dict[str, Any]] = None

class ResumeResponse(ResumeBase):
    id: UUID
    user_id: UUID
    status: str = Field(..., description="Status of the resume: draft, completed, etc.")
    ats_score: Optional[float] = None
    pdf_storage_path: Optional[str] = None # Added for Supabase storage path
    # Resume parsing fields
    parsed_content: Optional[Dict[str, Any]] = Field(None, description="Structured JSON content extracted by Claude")
    parsing_status: Optional[str] = Field("pending", description="Status of resume parsing")
    parsed_at: Optional[datetime] = Field(None, description="Timestamp when resume was parsed")
    parsing_error: Optional[str] = Field(None, description="Error message if parsing failed")
    # Resume scoring fields
    overall_score: Optional[float] = Field(None, ge=0, le=100, description="Overall resume score (0-100)")
    claude_score: Optional[float] = Field(None, ge=0, le=100, description="Format and structure score from Claude")
    openai_score: Optional[float] = Field(None, ge=0, le=100, description="Content score from OpenAI")
    career_overview_score: Optional[float] = Field(None, ge=0, le=100, description="Career overview section score")
    experience_score: Optional[float] = Field(None, ge=0, le=100, description="Experience section score")
    education_score: Optional[float] = Field(None, ge=0, le=100, description="Education section score")
    additional_qualifications_score: Optional[float] = Field(None, ge=0, le=100, description="Additional qualifications score")
    content_quality_score: Optional[float] = Field(None, ge=0, le=100, description="Content quality score")
    # Separate feedback fields for each AI service
    claude_feedback: Optional[Dict[str, Any]] = Field(None, description="Format and structure feedback from Claude")
    openai_feedback: Optional[Dict[str, Any]] = Field(None, description="Content quality feedback from OpenAI")
    scoring_feedback: Optional[Dict[str, Any]] = Field(None, description="Combined feedback (for backward compatibility)")
    scoring_status: Optional[str] = Field("pending", description="Status of resume scoring")
    last_scored_at: Optional[datetime] = Field(None, description="Timestamp of last scoring analysis")
    scoring_error: Optional[str] = Field(None, description="Error message if scoring failed")
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# Parsing and Scoring-specific models
class ParsingRequest(BaseModel):
    resume_id: UUID
    force_reparse: bool = False

class ParsingResponse(BaseModel):
    resume_id: UUID
    parsed_content: Dict[str, Any]
    parsing_status: str
    parsed_at: datetime
    parsing_error: Optional[str] = None

class ScoringRequest(BaseModel):
    resume_id: UUID
    job_description: Optional[str] = None
    force_rescore: bool = False
    use_parsed_content: bool = True

class ScoringResponse(BaseModel):
    resume_id: UUID
    overall_score: float = Field(..., ge=0, le=100)
    claude_score: float = Field(..., ge=0, le=100)
    openai_score: float = Field(..., ge=0, le=100)
    category_scores: Dict[str, float] = Field(..., description="Breakdown of OpenAI category scores")
    feedback: Dict[str, Any] = Field(..., description="Detailed feedback and suggestions")
    scored_at: datetime

class ScoringHistoryResponse(BaseModel):
    id: UUID
    resume_id: UUID
    # Parsing data
    parsed_content: Optional[Dict[str, Any]] = None
    parsing_version: Optional[str] = None
    # Scoring data
    overall_score: Optional[float] = None
    claude_score: Optional[float] = None
    openai_score: Optional[float] = None
    career_overview_score: Optional[float] = None
    experience_score: Optional[float] = None
    education_score: Optional[float] = None
    additional_qualifications_score: Optional[float] = None
    content_quality_score: Optional[float] = None
    # Separate feedback fields
    claude_feedback: Optional[Dict[str, Any]] = None
    openai_feedback: Optional[Dict[str, Any]] = None
    scoring_feedback: Optional[Dict[str, Any]] = None
    scoring_version: Optional[str] = None
    created_at: datetime

    class Config:
        orm_mode = True

class CategoryScoreBreakdown(BaseModel):
    career_overview: float = Field(..., ge=0, le=100, description="Career overview score (25% weight)")
    experience: float = Field(..., ge=0, le=100, description="Experience score (40% weight)")
    education: float = Field(..., ge=0, le=100, description="Education score (15% weight)")
    additional_qualifications: float = Field(..., ge=0, le=100, description="Additional qualifications score (10% weight)")
    content_quality: float = Field(..., ge=0, le=100, description="Content quality score (10% weight)")

# Feedback-specific models
class FeedbackIssue(BaseModel):
    issue: str = Field(..., description="Description of the issue found")
    severity: str = Field(..., description="Severity level: low, medium, high")
    recommendation: str = Field(..., description="Specific recommendation to fix the issue")
    section: Optional[str] = Field(None, description="Resume section where issue was found")
    current_text: Optional[str] = Field(None, description="Current problematic text")
    suggested_improvement: Optional[str] = Field(None, description="Suggested replacement text")

class ClaudeFeedback(BaseModel):
    format_issues: List[FeedbackIssue] = Field(default_factory=list, description="Format and structure issues")
    structure_issues: List[FeedbackIssue] = Field(default_factory=list, description="Resume structure issues")
    ats_compatibility: Dict[str, Any] = Field(..., description="ATS compatibility analysis")

class OpenAIFeedback(BaseModel):
    career_overview: List[FeedbackIssue] = Field(default_factory=list, description="Career overview issues")
    experience: List[FeedbackIssue] = Field(default_factory=list, description="Experience section issues")
    education: List[FeedbackIssue] = Field(default_factory=list, description="Education section issues")
    skills: List[FeedbackIssue] = Field(default_factory=list, description="Skills section issues")
    content_quality: List[FeedbackIssue] = Field(default_factory=list, description="Overall content quality issues")

class DetailedScoringResponse(BaseModel):
    resume_id: UUID
    overall_score: float = Field(..., ge=0, le=100)
    claude_analysis: Dict[str, Any] = Field(..., description="Claude format and structure analysis")
    openai_analysis: Dict[str, Any] = Field(..., description="OpenAI content analysis")
    category_breakdown: CategoryScoreBreakdown
    claude_feedback: ClaudeFeedback = Field(..., description="Structured Claude feedback")
    openai_feedback: OpenAIFeedback = Field(..., description="Structured OpenAI feedback")
    improvement_suggestions: List[str] = Field(..., description="Actionable improvement recommendations")
    ats_compatibility: Dict[str, Any] = Field(..., description="ATS compatibility analysis")
    scored_at: datetime
