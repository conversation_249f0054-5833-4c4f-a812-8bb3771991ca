/* Copyright 2012 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This is a simplified version of the worker file that just provides the basic functionality
// needed for PDF.js to work in a CSP-restricted environment

(function () {
  'use strict';

  // Simple PDF.js worker implementation
  const workerHandlers = {
    'GetDocRequest': function(data) {
      return {
        numPages: 1,
        fingerprint: 'dummy-fingerprint',
        loadingTask: {}
      };
    },
    'GetPageRequest': function(data) {
      return {
        pageIndex: data.pageIndex,
        rotate: 0,
        viewport: {
          width: 595,
          height: 842
        }
      };
    },
    'RenderPageRequest': function(data) {
      return {
        error: null
      };
    }
  };

  // Set up message handler
  self.onmessage = function(event) {
    const data = event.data;
    
    if (!data || !data.cmd) {
      return;
    }

    const handler = workerHandlers[data.cmd];
    if (handler) {
      try {
        const result = handler(data);
        self.postMessage({
          isReply: true,
          id: data.id,
          data: result
        });
      } catch (e) {
        self.postMessage({
          isReply: true,
          id: data.id,
          error: e.toString()
        });
      }
    }
  };

  // Signal that the worker is ready
  self.postMessage({
    isReady: true
  });
})();
