# CI/CD Testing Guide

This document outlines the steps to test the complete deployment process for both frontend and backend components of the AjiraPro project.

## Prerequisites

Before testing the CI/CD pipelines, ensure that the following GitHub secrets are configured:

### Frontend Deployment Secrets
- `CLOUDFLARE_API_TOKEN`: API token for Cloudflare
- `CL<PERSON><PERSON>DFLARE_ACCOUNT_ID`: Your Cloudflare account ID

### Backend Deployment Secrets
- `RA<PERSON>WAY_TOKEN`: API token for Railway
- `RAILWAY_PROJECT_ID`: Your Railway project ID (AjiraProMax)
- `RAILWAY_SERVICE_ID`: The ID of the specific service to deploy

## Testing Frontend Deployment

### 1. Feature Branch Testing

1. Create a new feature branch from the develop branch:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/test-ci-cd
   ```

2. Make a small change to a frontend file:
   ```bash
   # Example: Update a text string in a component
   # Edit AjiraPro/frontend/src/components/Header.tsx
   ```

3. Commit and push the changes:
   ```bash
   git add AjiraPro/frontend/src/components/Header.tsx
   git commit -m "feat(frontend): test CI/CD pipeline"
   git push -u origin feature/test-ci-cd
   ```

4. Create a pull request from `feature/test-ci-cd` to `develop` on GitHub.

5. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Frontend CI/CD" workflow is running
   - Verify that it completes the build and test steps successfully

### 2. Develop Branch Deployment

1. Merge the pull request to the develop branch.

2. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Frontend CI/CD" workflow is running
   - Verify that it completes the build, test, and deployment steps successfully

3. Check the staging deployment:
   - Go to your Cloudflare Pages dashboard
   - Verify that a new deployment has been created for the develop branch
   - Check that the changes are visible on the staging URL

### 3. Production Deployment

1. Create a release branch from develop:
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.x
   ```

2. Make any necessary version updates:
   ```bash
   # Update version numbers if needed
   ```

3. Commit and push the changes:
   ```bash
   git add .
   git commit -m "chore(release): prepare v1.0.x"
   git push -u origin release/v1.0.x
   ```

4. Create a pull request from `release/v1.0.x` to `main` on GitHub.

5. Merge the pull request to the main branch.

6. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Frontend CI/CD" workflow is running
   - Verify that it completes the build, test, and deployment steps successfully

7. Check the production deployment:
   - Go to your Cloudflare Pages dashboard
   - Verify that a new deployment has been created for the main branch
   - Check that the changes are visible on the production URL (fajirapro.com)

## Testing Backend Deployment

### 1. Feature Branch Testing

1. Create a new feature branch from the develop branch:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/test-backend-ci-cd
   ```

2. Make a small change to a backend file:
   ```bash
   # Example: Update a comment in a Python file
   # Edit AjiraPro/backend/app/main.py
   ```

3. Commit and push the changes:
   ```bash
   git add AjiraPro/backend/app/main.py
   git commit -m "feat(backend): test CI/CD pipeline"
   git push -u origin feature/test-backend-ci-cd
   ```

4. Create a pull request from `feature/test-backend-ci-cd` to `develop` on GitHub.

5. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Backend CI/CD" workflow is running
   - Verify that it completes the build and test steps successfully

### 2. Develop Branch Deployment

1. Merge the pull request to the develop branch.

2. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Backend CI/CD" workflow is running
   - Verify that it completes the build, test, and deployment steps successfully

3. Check the staging deployment:
   - Go to your Railway dashboard
   - Verify that a new deployment has been created for the staging environment
   - Check that the changes are deployed correctly

### 3. Production Deployment

1. Create a release branch from develop (if not already done for frontend):
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.x
   ```

2. Make any necessary version updates:
   ```bash
   # Update version numbers if needed
   ```

3. Commit and push the changes:
   ```bash
   git add .
   git commit -m "chore(release): prepare v1.0.x"
   git push -u origin release/v1.0.x
   ```

4. Create a pull request from `release/v1.0.x` to `main` on GitHub.

5. Merge the pull request to the main branch.

6. Verify that the CI/CD workflow runs automatically:
   - Go to the "Actions" tab in your GitHub repository
   - Check that the "Backend CI/CD" workflow is running
   - Verify that it completes the build, test, and deployment steps successfully

7. Check the production deployment:
   - Go to your Railway dashboard
   - Verify that a new deployment has been created for the production environment
   - Check that the changes are deployed correctly

## Troubleshooting

### Common Issues

1. **Workflow not triggered**:
   - Check that the file paths in the workflow trigger match the files you modified
   - Verify that you pushed to the correct branch

2. **Build failures**:
   - Check the build logs for errors
   - Verify that all dependencies are correctly installed
   - Check for linting or test failures

3. **Deployment failures**:
   - Verify that all required secrets are configured correctly
   - Check for permission issues with the deployment tokens
   - Verify that the project names and service IDs are correct

### Rollback Procedure

If a deployment fails or introduces issues:

1. **Frontend Rollback**:
   - Go to the Cloudflare Pages dashboard
   - Find the previous successful deployment
   - Click "Rollback to this deployment"

2. **Backend Rollback**:
   - Go to the Railway dashboard
   - Select the service with issues
   - Go to the "Deployments" tab
   - Find the last working deployment
   - Click on the three dots menu and select "Rollback to this deployment"
