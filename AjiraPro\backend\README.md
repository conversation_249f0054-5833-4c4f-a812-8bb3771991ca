# AjiraPro Backend

This is the backend API for AjiraPro, an AI-powered resume and CV builder.

## Deployment on Railway

The AjiraPro backend is deployed on Railway as part of the AjiraProMax project.

### Project Structure

The AjiraProMax project on Railway consists of three services:

1. **FastAPI Service**
   - Handles API requests
   - Runs the main FastAPI application
   - Provides endpoints for resume/CV generation, user management, etc.

2. **Redis Service**
   - Provides message broker functionality for Celery
   - Stores task queue information
   - Handles communication between FastAPI and Celery worker

3. **Celery Worker Service**
   - Processes background tasks
   - Handles resource-intensive operations like resume generation
   - Communicates with FastAPI through Redis

### Deployment Configuration

Each service is configured as follows:

1. **FastAPI Service**
   - Source: GitHub repository (ProDevDenis/fAjiraPro)
   - Root Directory: `AjiraPro/backend/`
   - Start Command: `/app/web.sh`

2. **Redis Service**
   - Uses Railway's Redis plugin
   - No additional configuration needed

3. **Celery Worker Service**
   - Source: Same GitHub repository
   - Root Directory: `AjiraPro/backend/`
   - Start Command: `/app/worker.sh`

### Required Environment Variables

The following environment variables must be set for both the FastAPI and Celery Worker services:

| Variable | Description | Notes |
|----------|-------------|-------|
| `SUPABASE_URL` | Your Supabase project URL | Required for database access |
| `SUPABASE_KEY` | Your Supabase anon/public key | **Not** the service role key |
| `REDIS_URL` | URL for Redis | Automatically set by Railway when using Redis plugin |
| `ENVIRONMENT` | Application environment | Set to `production` for deployment |
| `DEBUG` | Debug mode | Set to `False` for production |
| `OPENAI_API_KEY` | Your OpenAI API key | Required for AI features |
| `PORT` | Port for the FastAPI service | Automatically set by Railway |

### Deployment Process

1. Push changes to the GitHub repository
2. Railway automatically detects changes and starts the deployment process
3. The Docker image is built using the Dockerfile in the backend directory
4. The services are started with their respective commands
5. Environment variables are injected into the containers

## Local Development with Docker

1. Build the Docker image:
   ```
   docker build -t ajirapro-backend .
   ```

2. Run the FastAPI server:
   ```
   docker run -p 8000:8000 --env-file .env ajirapro-backend /app/web.sh
   ```

3. Run the Celery worker:
   ```
   docker run --env-file .env ajirapro-backend /app/worker.sh
   ```

4. Run with Docker Compose (recommended):
   ```
   docker-compose up
   ```

## Local Development without Docker

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Set up environment variables in `.env` file:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_anon_key
   REDIS_URL=redis://localhost:6379/0
   ENVIRONMENT=development
   DEBUG=True
   OPENAI_API_KEY=your_openai_api_key
   ```

3. Run the FastAPI server:
   ```
   uvicorn app.main:app --reload
   ```

4. Run the Celery worker:
   ```
   celery -A app.celery_worker.celery worker --loglevel=info
   ```

## API Documentation

When running in development mode, API documentation is available at:
- Swagger UI: `/api/docs`
- ReDoc: `/api/redoc`

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   - Check that all required environment variables are set in Railway
   - Verify the variable names are correct (case-sensitive)
   - Ensure there are no extra spaces or characters

2. **Deployment Failures**
   - Check the logs in Railway for specific error messages
   - Verify that the Docker build is successful
   - Ensure the start commands are correct

3. **Service Communication Issues**
   - Verify that Redis is running and accessible
   - Check that the REDIS_URL is correctly set
   - Ensure Celery worker is connected to Redis

### Debugging

The application includes several debugging endpoints:

- `/` - Shows basic application information and environment variable status
- `/health` - Provides detailed health check information for all services
