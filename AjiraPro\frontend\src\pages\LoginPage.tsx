import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const LoginPage: React.FC = () => {
  const { isDark } = useTheme();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [magicLinkMode, setMagicLinkMode] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, isAuthenticated, profile, signInWithMagicLink, signInWithGoogle } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Check if user needs to complete onboarding
      if (profile && !profile.has_completed_onboarding) {
        navigate('/onboarding');
      } else {
        // Get the redirect path from location state or default to dashboard
        const from = location.state?.from?.pathname || '/dashboard';
        navigate(from);
      }
    }
  }, [isAuthenticated, profile, navigate, location]);

  // Check for magic=true parameter
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const magic = searchParams.get('magic');

    if (magic === 'true') {
      setMagicLinkMode(true);

      // Clean up the URL to prevent infinite redirects
      if (location.search) {
        // Replace the current URL with a clean version without query parameters
        window.history.replaceState({}, document.title, location.pathname);
      }
    }
  }, [location]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (magicLinkMode) {
        // Send magic link
        const { error } = await signInWithMagicLink(email);

        if (error) {
          throw error;
        }

        setSuccess('Magic link sent! Please check your email to sign in.');
        setLoading(false);
      } else {
        // Regular password login
        const { error } = await signIn(email, password);

        if (error) {
          throw error;
        }

        // The redirect will be handled by the useEffect above
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred during login');
      setLoading(false);
    }
  };

  const toggleMagicLinkMode = () => {
    setMagicLinkMode(!magicLinkMode);
    setError(null);
    setSuccess(null);
  };

  const handleGoogleSignIn = async () => {
    try {
      setGoogleLoading(true);
      setError(null);

      const { error } = await signInWithGoogle();

      if (error) {
        throw error;
      }

      // The redirect will be handled by the OAuth provider
      // and the GoogleCallback component
    } catch (error: any) {
      setError(error.message || 'An error occurred during Google sign in');
      setGoogleLoading(false);
    }
  };

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'} flex flex-col justify-center py-12 sm:px-6 lg:px-8`}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className={`mt-6 text-center text-3xl font-extrabold ${isDark ? 'text-white' : 'text-gray-900'}`}>
          Sign in to your account
        </h2>
        <p className={`mt-2 text-center text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
          Or{' '}
          <Link to="/register" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className={`${isDark ? 'bg-dark-200 border border-dark-300 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow'} py-8 px-4 sm:rounded-lg sm:px-10`}>
          {error && (
            <div className={`${isDark ? 'bg-red-900/30 border-red-700' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-6`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className={`h-5 w-5 ${isDark ? 'text-red-500' : 'text-red-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${isDark ? 'text-red-400' : 'text-red-700'}`}>{error}</p>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className={`${isDark ? 'bg-green-900/30 border-green-700' : 'bg-green-50 border-green-400'} border-l-4 p-4 mb-6`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className={`h-5 w-5 ${isDark ? 'text-green-500' : 'text-green-400'}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${isDark ? 'text-green-400' : 'text-green-700'}`}>{success}</p>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleLogin}>
            <div>
              <label htmlFor="email" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                />
              </div>
            </div>

            {!magicLinkMode && (
              <div>
                <label htmlFor="password" className={`block text-sm font-medium ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Password
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required={!magicLinkMode}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`appearance-none block w-full px-3 py-2 border ${isDark ? 'bg-dark-300 border-dark-400 text-white placeholder-gray-500' : 'border-gray-300 placeholder-gray-400'} rounded-md shadow-sm focus:outline-none ${isDark ? 'focus:ring-neon-cyan focus:border-neon-cyan' : 'focus:ring-blue-500 focus:border-blue-500'} sm:text-sm`}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              {!magicLinkMode && (
                <>
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className={`h-4 w-4 ${isDark ? 'text-neon-cyan focus:ring-neon-cyan border-dark-400' : 'text-blue-600 focus:ring-blue-500 border-gray-300'} rounded`}
                    />
                    <label htmlFor="remember-me" className={`ml-2 block text-sm ${isDark ? 'text-gray-200' : 'text-gray-900'}`}>
                      Remember me
                    </label>
                  </div>

                  <div className="text-sm">
                    <Link to="/forgot-password" className={`font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}>
                      Forgot your password?
                    </Link>
                  </div>
                </>
              )}

              {magicLinkMode && (
                <div className={`text-sm w-full text-center ${isDark ? 'text-gray-300' : ''}`}>
                  <span>We'll send you a magic link to sign in without a password.</span>
                </div>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className={`w-full flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                  isDark
                    ? 'bg-dark-300 text-neon-cyan border-neon-cyan hover:bg-dark-400 focus:ring-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                    : 'bg-blue-600 text-white border-transparent hover:bg-blue-700 focus:ring-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50`}
              >
                {loading ? (magicLinkMode ? 'Sending...' : 'Signing in...') : (magicLinkMode ? 'Send Magic Link' : 'Sign in')}
              </button>
            </div>

            <div className="text-center">
              <button
                type="button"
                onClick={toggleMagicLinkMode}
                className={`text-sm font-medium ${isDark ? 'text-neon-cyan hover:text-neon-blue' : 'text-blue-600 hover:text-blue-500'}`}
              >
                {magicLinkMode ? 'Sign in with password instead' : 'Sign in with a magic link instead'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className={`w-full border-t ${isDark ? 'border-dark-400' : 'border-gray-300'}`}></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className={`px-2 ${isDark ? 'bg-dark-200 text-gray-400' : 'bg-white text-gray-500'}`}>Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 gap-3">
              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={googleLoading || loading}
                className={`w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${
                  isDark
                    ? 'border-dark-400 bg-dark-300 text-gray-300 hover:bg-dark-400'
                    : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {googleLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-gray-500 rounded-full"></div>
                    <span>Connecting to Google...</span>
                  </div>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z" />
                    </svg>
                    <span>Sign in with Google</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
