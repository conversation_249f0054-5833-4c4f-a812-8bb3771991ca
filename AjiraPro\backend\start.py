#!/usr/bin/env python3
"""
Simple startup script for Railway deployment
"""

import os
import sys
import uvicorn

# Set environment variables for Railway
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "False")

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    
    print(f"Starting FastAPI server on port {port}")
    print(f"Environment: {os.environ.get('ENVIRONMENT', 'Not set')}")
    print(f"CORS_ORIGINS: {os.environ.get('CORS_ORIGINS', 'Not set')}")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True
    )
